{
    tool_target_bass_windows_x64_ld = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\link.exe]],
        toolname = "link",
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            name = "msvc",
            cachekey = "msvc_arch_x64_plat_windows"
        }
    },
    fpc_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __global = true,
        __checked = true
    },
    msvc_arch_x64_plat_windows = {
        arch = "x64",
        vcvars = {
            VSCMD_ARG_HOST_ARCH = "x64",
            WindowsSdkVerBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\]],
            DevEnvdir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\]],
            VCToolsInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\]],
            WindowsSdkBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\]],
            VSCMD_ARG_app_plat = "Desktop",
            VS170COMNTOOLS = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\]],
            VCToolsVersion = "14.43.34808",
            WindowsLibPath = [[C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.22621.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.22621.0]],
            INCLUDE = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\ATLMFC\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um]],
            WindowsSDKVersion = "10.0.22621.0",
            LIB = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\lib\x64;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64;C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64;C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64]],
            ExtensionSdkDir = [[C:\Program Files (x86)\Microsoft SDKs\Windows Kits\10\ExtensionSDKs]],
            UniversalCRTSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            VSInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\]],
            VCToolsRedistDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Redist\MSVC\14.42.34433\]],
            PATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\VCPackages;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TeamFoundation\Team Explorer;C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\bin\Roslyn;C:\Program Files (x86)\Microsoft SDKs\Windows\v10.0A\bin\NETFX 4.8 Tools\x64\;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\FSharp\Tools;C:\Program Files\Microsoft Visual Studio\2022\Community\Team Tools\DiagnosticsHub\Collector;C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\;C:\Program Files\Eclipse Adoptium\jre-*********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jre-**********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-**********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jre-*********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-8.0.432.6-hotspot\bin;C:\Program Files\Eclipse Adoptium\jre-8.0.432.6-hotspot\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\Program Files\Tailscale\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\WireGuard\;C:\Program Files\GitHub CLI\;C:\Program Files\PowerShell\7\;C:\WINDOWS\System32\OpenSSH\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\eSpeak NG\;C:\Program Files\Oracle\VirtualBox;C:\Program Files\CMake\bin;C:\Program Files\Go\bin;C:\Program Files\xmake;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\Desktop\File\ffmpeg\bin;C:\Users\<USER>\Desktop\File\yt-dlp;C:\Users\<USER>\.local\bin;C:\Users\<USER>\Desktop\File\neovim\bin;C:\Users\<USER>\Desktop\File\adb;C:\Program Files\7-Zip\;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\Desktop\File\SteamCMD;C:\Users\<USER>\AppData\Roaming\uv\python\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Nmap;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\.dotnet\tools;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\Linux\bin\ConnectionManagerExe;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\vcpkg]],
            LIBPATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\lib\x86\store\references;C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.22621.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.22621.0;C:\Windows\Microsoft.NET\Framework64\v4.0.30319]],
            VSCMD_ARG_TGT_ARCH = "x64",
            VSCMD_VER = "17.13.6",
            WindowsSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            VisualStudioVersion = "17.0",
            VCIDEInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\]],
            UCRTVersion = "10.0.22621.0",
            VCInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\]]
        },
        vs_sdkver = "10.0.22621.0",
        vs = "2022",
        plat = "windows",
        __checked = "2022",
        __global = true,
        vcarchs = {
            "arm",
            "arm64",
            "arm64ec",
            "x64",
            "x86"
        },
        vs_toolset = "14.43.34808"
    },
    tool_target_bassmidi_windows_x64_cxx = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\cl.exe]],
        toolname = "cl",
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            name = "msvc",
            cachekey = "msvc_arch_x64_plat_windows"
        }
    },
    yasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __global = true,
        __checked = true
    },
    go_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __global = true,
        __checked = true
    },
    rust_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __global = true,
        __checked = true
    },
    nim_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __global = true,
        __checked = false
    },
    tool_target_bass_windows_x64_cc = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\cl.exe]],
        toolname = "cl",
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            name = "msvc",
            cachekey = "msvc_arch_x64_plat_windows"
        }
    },
    tool_target_bassmidi_windows_x64_ld = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\link.exe]],
        toolname = "link",
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            name = "msvc",
            cachekey = "msvc_arch_x64_plat_windows"
        }
    },
    tool_target_bassmidi_windows_x64_cc = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\cl.exe]],
        toolname = "cl",
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            name = "msvc",
            cachekey = "msvc_arch_x64_plat_windows"
        }
    },
    tool_target_bassmidi_windows_x64_sh = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\link.exe]],
        toolname = "link",
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            name = "msvc",
            cachekey = "msvc_arch_x64_plat_windows"
        }
    },
    tool_target_bass_windows_x64_cxx = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\cl.exe]],
        toolname = "cl",
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            name = "msvc",
            cachekey = "msvc_arch_x64_plat_windows"
        }
    },
    swift_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __global = true,
        __checked = true
    },
    cuda_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __global = true,
        __checked = true
    },
    nasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __global = true,
        __checked = true
    },
    tool_target_bass_windows_x64_sh = {
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\link.exe]],
        toolname = "link",
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            name = "msvc",
            cachekey = "msvc_arch_x64_plat_windows"
        }
    },
    gfortran_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __global = true,
        __checked = true
    }
}