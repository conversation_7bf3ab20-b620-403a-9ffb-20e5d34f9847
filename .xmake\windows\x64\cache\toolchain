{
    gfortran_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true,
        __global = true
    },
    msvc_arch_x64_plat_windows = {
        vs = "2022",
        plat = "windows",
        vs_sdkver = "10.0.22621.0",
        __global = true,
        vcvars = {
            VSCMD_VER = "17.10.3",
            ExtensionSdkDir = [[C:\Program Files (x86)\Microsoft SDKs\Windows Kits\10\ExtensionSDKs]],
            VS170COMNTOOLS = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\Tools\]],
            PATH = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\bin\HostX64\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\VCPackages;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\bin\Roslyn;C:\Program Files (x86)\Microsoft SDKs\Windows\v10.0A\bin\NETFX 4.8 Tools\x64\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\Extensions\Microsoft\CodeCoverage.Console;C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\Tools\;C:\Program Files\Google\Chrome\Application;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Eclipse Adoptium\jdk-8.0.392.8-hotspot\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.4\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.4\libnvvp;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\libnvvp;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\Program Files\Oculus\Support\oculus-runtime;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files (x86)\dotnet\;C:\Program Files\dotnet\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\NVIDIA Corporation\Nsight Compute 2021.2.0\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\Program Files\nodejs\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\CMake\bin;C:\Program Files\Git\cmd;C:\Program Files\Go\bin;C:\Program Files\GitHub CLI\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\Downloads\fluidsynth-2.3.2-win10-x64\bin\;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Program Files\LLVM\bin;D:\Downloads\w64debkit\w64devkit\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;D:\Downloads\ninja-win;C:\TDM-GCC-64\bin;C:\Program Files (x86)\Nmap;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Mono\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\Linux\bin\ConnectionManagerExe]],
            INCLUDE = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\include;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um]],
            VSCMD_ARG_HOST_ARCH = "x64",
            VSCMD_ARG_app_plat = "Desktop",
            VCToolsRedistDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Redist\MSVC\14.40.33807\]],
            VisualStudioVersion = "17.0",
            WindowsSdkVerBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\]],
            LIBPATH = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\lib\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\lib\x86\store\references;C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.22621.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.22621.0;C:\Windows\Microsoft.NET\Framework64\v4.0.30319]],
            VCToolsInstallDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\]],
            VSInstallDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\]],
            LIB = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\lib\x64;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64;C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64;C:\Program Files (x86)\Windows Kits\10\\lib\10.0.22621.0\\um\x64]],
            VCIDEInstallDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\]],
            UCRTVersion = "10.0.22621.0",
            UniversalCRTSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]],
            WindowsLibPath = [[C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.22621.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.22621.0]],
            WindowsSDKVersion = "10.0.22621.0",
            DevEnvdir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\]],
            VSCMD_ARG_TGT_ARCH = "x64",
            VCToolsVersion = "14.40.33807",
            VCInstallDir = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\]],
            WindowsSdkBinPath = [[C:\Program Files (x86)\Windows Kits\10\bin\]],
            WindowsSdkDir = [[C:\Program Files (x86)\Windows Kits\10\]]
        },
        arch = "x64",
        vcarchs = {
            "arm",
            "arm64",
            "arm64ec",
            "x64",
            "x86"
        },
        vs_toolset = "14.40.33807",
        __checked = "2022"
    },
    nim_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false,
        __global = true
    },
    yasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true,
        __global = true
    },
    tool_target_bassmidi_windows_x64_cc = {
        toolname = "cl",
        toolchain_info = {
            arch = "x64",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            plat = "windows"
        },
        program = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\bin\HostX64\x64\cl.exe]]
    },
    go_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true,
        __global = true
    },
    fpc_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true,
        __global = true
    },
    tool_target_bassmidi_windows_x64_sh = {
        toolname = "link",
        toolchain_info = {
            arch = "x64",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            plat = "windows"
        },
        program = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\bin\HostX64\x64\link.exe]]
    },
    rust_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true,
        __global = true
    },
    tool_target_bass_windows_x64_sh = {
        toolname = "link",
        toolchain_info = {
            arch = "x64",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            plat = "windows"
        },
        program = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\bin\HostX64\x64\link.exe]]
    },
    swift_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true,
        __global = true
    },
    tool_target_bass_windows_x64_ld = {
        toolname = "link",
        toolchain_info = {
            arch = "x64",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            plat = "windows"
        },
        program = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\bin\HostX64\x64\link.exe]]
    },
    tool_target_bass_windows_x64_cc = {
        toolname = "cl",
        toolchain_info = {
            arch = "x64",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            plat = "windows"
        },
        program = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\bin\HostX64\x64\cl.exe]]
    },
    tool_target_bassmidi_windows_x64_cxx = {
        toolname = "cl",
        toolchain_info = {
            arch = "x64",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            plat = "windows"
        },
        program = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\bin\HostX64\x64\cl.exe]]
    },
    tool_target_bassmidi_windows_x64_ld = {
        toolname = "link",
        toolchain_info = {
            arch = "x64",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            plat = "windows"
        },
        program = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\bin\HostX64\x64\link.exe]]
    },
    tool_target_bass_windows_x64_cxx = {
        toolname = "cl",
        toolchain_info = {
            arch = "x64",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc",
            plat = "windows"
        },
        program = [[C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.40.33807\bin\HostX64\x64\cl.exe]]
    },
    nasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true,
        __global = true
    },
    cuda_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true,
        __global = true
    }
}