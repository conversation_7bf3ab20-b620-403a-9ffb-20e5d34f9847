#include "event.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

// イベントリスナーの最大数
#define MAX_LISTENERS 64

// イベント名の最大長
#define MAX_EVENT_NAME_LENGTH 64

// イベントリスナー構造体
typedef struct {
    int id;
    char eventName[MAX_EVENT_NAME_LENGTH];
    EventCallback callback;
    bool active;
} Listener;

// イベント管理用の静的変数
static Listener listeners[MAX_LISTENERS];
static int nextListenerId = 1;
static bool initialized = false;

/**
 * イベントシステムの初期化
 */
void Event_Init(void) {
    if (initialized) {
        return;
    }
    
    // すべてのリスナーを初期化
    for (int i = 0; i < MAX_LISTENERS; i++) {
        listeners[i].id = 0;
        listeners[i].eventName[0] = '\0';
        listeners[i].callback = NULL;
        listeners[i].active = false;
    }
    
    nextListenerId = 1;
    initialized = true;
}

/**
 * イベントシステムの終了処理
 */
void Event_Shutdown(void) {
    if (!initialized) {
        return;
    }
    
    // すべてのリスナーをクリア
    for (int i = 0; i < MAX_LISTENERS; i++) {
        listeners[i].id = 0;
        listeners[i].eventName[0] = '\0';
        listeners[i].callback = NULL;
        listeners[i].active = false;
    }
    
    initialized = false;
}

/**
 * 空きリスナースロットを探す
 * @return 見つかったスロットのインデックス、見つからない場合は-1
 */
static int FindFreeSlot(void) {
    for (int i = 0; i < MAX_LISTENERS; i++) {
        if (!listeners[i].active) {
            return i;
        }
    }
    return -1;
}

/**
 * リスナーIDからインデックスを取得
 * @param id リスナーID
 * @return インデックス、見つからない場合は-1
 */
static int FindListenerIndex(int id) {
    if (id <= 0) {
        return -1;
    }
    
    for (int i = 0; i < MAX_LISTENERS; i++) {
        if (listeners[i].active && listeners[i].id == id) {
            return i;
        }
    }
    return -1;
}

/**
 * イベントリスナーの登録
 * @param eventName 監視するイベント名
 * @param callback コールバック関数
 * @return リスナーID（0より大きい値）、エラー時は0
 */
int Event_Listen(const char* eventName, EventCallback callback) {
    if (!initialized) {
        Event_Init();
    }
    
    if (eventName == NULL || callback == NULL) {
        return 0;
    }
    
    int slot = FindFreeSlot();
    if (slot < 0) {
        return 0; // スロットがいっぱい
    }
    
    // イベント名のコピー（範囲チェック付き）
    strncpy(listeners[slot].eventName, eventName, MAX_EVENT_NAME_LENGTH - 1);
    listeners[slot].eventName[MAX_EVENT_NAME_LENGTH - 1] = '\0';
    
    listeners[slot].callback = callback;
    listeners[slot].id = nextListenerId++;
    listeners[slot].active = true;
    
    return listeners[slot].id;
}

/**
 * イベントリスナーの削除
 * @param listenerId リスナーID
 * @return 成功した場合はtrue
 */
bool Event_Remove(int listenerId) {
    if (!initialized || listenerId <= 0) {
        return false;
    }
    
    int index = FindListenerIndex(listenerId);
    if (index < 0) {
        return false;
    }
    
    listeners[index].active = false;
    listeners[index].callback = NULL;
    listeners[index].eventName[0] = '\0';
    
    return true;
}

/**
 * イベント発火
 * @param eventName イベント名
 * @param data イベントデータ
 */
void Event_Trigger(const char* eventName, void* data) {
    if (!initialized || eventName == NULL) {
        return;
    }
    
    // すべてのアクティブなリスナーに通知
    for (int i = 0; i < MAX_LISTENERS; i++) {
        if (listeners[i].active && 
            listeners[i].callback != NULL && 
            strcmp(listeners[i].eventName, eventName) == 0) {
            
            // コールバック呼び出し
            listeners[i].callback(eventName, data);
        }
    }
}