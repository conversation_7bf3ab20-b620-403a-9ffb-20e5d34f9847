{
    __toolchains_windows_x64 = {
        "msvc",
        "yasm",
        "nasm",
        "cuda",
        "rust",
        "swift",
        "go",
        "gfortran",
        "fpc"
    },
    arch = "x64",
    buildir = "build",
    ccache = true,
    host = "windows",
    kind = "static",
    mingw = [[D:\Downloads\w64debkit\w64devkit\bin]],
    mode = "release",
    ndk_stdcxx = true,
    network = "public",
    plat = "windows",
    proxy_pac = "pac.lua",
    theme = "default",
    vs = "2022"
}