#ifndef ksynth_h
#define ksynth_h

#pragma once

/* Warning, this file is autogenerated by cbindgen. Don't modify this manually. */

#include <stdarg.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <stdlib.h>


typedef struct KSynthPtr KSynthPtr;

typedef struct KSynthSampleMapPtr KSynthSampleMapPtr;

typedef struct KSynthSampleLoop {
  size_t start;
  size_t end;
} KSynthSampleLoop;

#ifdef __cplusplus
extern "C" {
#endif // __cplusplus

/**
 * Calculates the memory usage for a given number of voices.
 *
 * # Arguments
 * `voice_count` - The number of voices to calculate memory usage for.
 * # Returns
 * The estimated memory usage in bytes for the specified number of voices.
 */
size_t ksynth_calculate_voice_memory_usage(size_t voice_count);

/**
 * Fills the provided buffer with audio data generated by the synthesizer.
 *
 * The buffer should be appropriately sized based on the synthesizer's channel count
 * (e.g., for stereo, `buffer_size` should be `num_frames * 2`).
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 * `buffer` - A pointer to a buffer that will be filled with audio data (`f32`).
 * `buffer_size` - The size of the buffer in number of `f32` samples.
 *
 * # Returns
 * `true` (1) if the buffer was successfully filled, or `false` (0) if the operation failed (e.g., invalid handle).
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` previously returned by `ksynth_new`.
 * `buffer` must be a valid pointer to a mutable block of memory of at least `buffer_size * std::mem::size_of::<f32>()` bytes.
 * `buffer_size` must accurately reflect the number of `f32` samples the buffer can hold.
 */
bool ksynth_fill_buffer(struct KSynthPtr *synth_ptr,
                        float *buffer,
                        size_t buffer_size);

/**
 * Frees the memory associated with a `KSynth` instance previously created by `ksynth_new`.
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` previously returned by `ksynth_new`.
 * After calling this function, the `synth_ptr` becomes invalid and must not be used again.
 */
void ksynth_free(struct KSynthPtr *synth_ptr);

/**
 * Frees a string previously returned by ksynth_get_git_commit_hash.
 *
 * # Arguments
 * `string` - The pointer to the string to free.
 */
void ksynth_free_git_commit_hash_string(char *string);

/**
 * Retrieves the fade-out sample count (in sample units).
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 *
 * # Returns
 * The fade-out sample count, or `-1` if the `synth_ptr` is invalid or fade-out is not set.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` previously returned by `ksynth_new`.
 */
int64_t ksynth_get_fade_out_sample(struct KSynthPtr *synth_ptr);

/**
 * Returns the git commit hash used at build time.
 *
 * If successful, returns a pointer to a null-terminated C string containing the commit hash.
 * The caller is responsible for freeing this string using a corresponding free function
 * (e.g., `ksynth_free_string` if you provide one, or standard `free` if allocated via C allocator).
 * If the hash is not available, returns a null pointer.
 *
 * # Returns
 * `*const c_char` - Pointer to a C string (must be freed by the caller) or null.
 */
const char *ksynth_get_git_commit_hash(void);

/**
 * Retrieves the maximum polyphony (maximum number of voices the synthesizer can handle).
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 *
 * # Returns
 * The maximum polyphony limit, or `-1` if the `synth_ptr` is invalid.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` previously returned by `ksynth_new`.
 */
int32_t ksynth_get_max_polyphony(struct KSynthPtr *synth_ptr);

/**
 * Returns the highest polyphony value that can be configured in ksynth.
 *
 * # Returns
 * `u32` - Maximum configurable voice count.
 */
uint32_t ksynth_get_max_supported_polyphony(void);

/**
 * Retrieves the current polyphony (number of voices being actively used).
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 *
 * # Returns
 * The current number of active voices, or `-1` if the `synth_ptr` is invalid.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` previously returned by `ksynth_new`.
 */
int32_t ksynth_get_polyphony(struct KSynthPtr *synth_ptr);

/**
 * Retrieves the polyphony counts for each MIDI channel.
 *
 * # Arguments
 * * `synth_ptr` - A pointer to the KSynth instance.
 * * `out_polyphony_counts` - A pointer to an array of at least 16 u32 elements where the polyphony counts will be stored.
 *
 * # Returns
 * `true` if successful, `false` if either pointer is invalid.
 *
 * # Safety
 * * `synth_ptr` must be a valid pointer to a `KSynth` instance previously created by `ksynth_new`.
 * * `out_polyphony_counts` must point to a buffer capable of holding at least `16 * std::mem::size_of::<u32>()` bytes.
 */
bool ksynth_get_polyphony_per_channel(struct KSynthPtr *synth_ptr,
                                      uint32_t *out_polyphony_counts);

/**
 * Retrieves the current rendering time as a percentage of the available time per buffer.
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 *
 * # Returns
 * The rendering time percentage (e.g., 0.1 for 10%), or `-1.0` if the `synth_ptr` is invalid.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` previously returned by `ksynth_new`.
 */
float ksynth_get_rendering_time(struct KSynthPtr *synth_ptr);

/**
 * Retrieves the current velocity curve from the KSynth instance and copies it into a C buffer.
 *
 * The velocity curve is an array of 128 `f32` values, where the index represents
 * the MIDI velocity (0-127) and the value is the corresponding amplitude scaling factor.
 *
 * # Arguments
 * `synth_ptr` - A pointer to the `KSynth` instance.
 * `out_velocity_curve` - A pointer to a C array of `float` (at least 128 elements)
 *                          where the velocity curve data will be copied.
 *
 * # Returns
 * `true` (1) if the velocity curve was successfully copied.
 * `false` (0) if `synth_ptr` or `out_velocity_curve` is null.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` instance previously returned by `ksynth_new`.
 * `out_velocity_curve` must be a valid pointer to a mutable block of memory capable of
 *   holding at least `128 * std::mem::size_of::<f32>()` bytes. The caller is responsible
 *   for allocating and managing this buffer.
 */
bool ksynth_get_velocity_curve(struct KSynthPtr *synth_ptr, float *out_velocity_curve);

/**
 * Returns the memory size in bytes required for a single voice instance.
 *
 * # Returns
 * The memory usage in bytes for one voice.
 */
size_t ksynth_get_voice_size_byte(void);

/**
 * Converts a given duration in milliseconds to the corresponding sample count based on the sample rate.
 *
 * # Arguments
 * `sample_rate` - The sample rate of the audio in samples per second (Hz). This is typically a standard audio sample rate like 44,100 Hz or 48,000 Hz.
 * `duration_ms` - The duration of the audio or signal in milliseconds (ms). This represents how long the audio lasts.
 *
 * # Returns
 * `u32` - The number of samples corresponding to the given duration in milliseconds, based on the specified sample rate.
 */
uint32_t ksynth_ms_to_sample(uint32_t sample_rate,
                             uint32_t duration_ms);

/**
 * Creates a new `KSynth` instance using a pre-built shared sample map.
 *
 * # Arguments
 * `sample_rate` - The sample rate of the synthesizer in Hz.
 * `num_channel` - The number of channels (1 for mono, 2 for stereo).
 * `max_polyphony` - The maximum number of voices.
 * `fade_out_sample` - The number of samples over which to apply the fade-out.
 *                     Set to 0 to disable fade-out (no fading).
 * `sample_map_ptr` - A pointer to the shared sample map created by `ksynth_sample_map_new`.
 *
 * # Returns
 * A pointer to a new `KSynth` instance, or `null` on failure.
 * The returned pointer must be freed using `ksynth_free`.
 *
 * # Ownership & Memory Management
 * This function **shares** the provided sample map using `Arc::clone()`.
 * It does **not** take ownership of the `sample_map_ptr` itself.
 * The caller is still responsible for calling `ksynth_sample_map_free` on the
 * `sample_map_ptr` when it's no longer needed by the C application,
 * even after creating KSynth instances with it.
 *
 * # Safety
 * `sample_map_ptr` must be a valid pointer returned by `ksynth_sample_map_new`.
 */
struct KSynthPtr *ksynth_new(uint32_t sample_rate,
                             uint8_t num_channel,
                             uint32_t max_polyphony,
                             uint64_t fade_out_sample,
                             const struct KSynthSampleMapPtr *sample_map_ptr);

/**
 * Queues a MIDI command for processing by the synthesizer.
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 * `midi_cmd` - The MIDI command to be processed.
 *              Encoded as: `(status | (data1 << 8) | (data2 << 16))`
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` previously returned by `ksynth_new`.
 */
void ksynth_queue_midi_cmd(struct KSynthPtr *synth_ptr, uint32_t midi_cmd);

/**
 * Resets the velocity curve of the KSynth instance to its default setting.
 *
 * # Arguments
 * `synth_ptr` - A pointer to the `KSynth` instance.
 *
 * # Returns
 * `true` (1) if the velocity curve was successfully reset.
 * `false` (0) if `synth_ptr` is null.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` instance previously returned by `ksynth_new`.
 */
bool ksynth_reset_velocity_curve(struct KSynthPtr *synth_ptr);

/**
 * Adds or replaces a sample in the specified sample map.
 *
 * This function copies the provided sample data into the map.
 *
 * # Arguments
 * `map_ptr` - Pointer to the sample map created by `ksynth_sample_map_new`.
 * `key` - The MIDI note value (`u8`) to associate with this sample.
 * `sample_rate` - The sample rate of the sample in Hz.
 * `sample_data_ptr` - Pointer to the raw sample data (i16). Interleaved for stereo.
 * `channel` - Number of channels (1 for mono, 2 for stereo).
 * `num_samples` - Total number of i16 values.
 * `sample_loop` - Pointer to a `KSynthSampleLoop` or `null`.
 *
 * # Returns
 * `true` (1) on success, `false` (0) on failure (invalid arguments, map pointer, etc.).
 *
 * # Safety
 * `map_ptr` must be a valid pointer returned by `ksynth_sample_map_new`.
 * `sample_data_ptr` must point to `num_samples` valid i16 values.
 * `sample_loop`, if not null, must point to a valid `KSynthSampleLoop`.
 */
bool ksynth_sample_map_add_sample(struct KSynthSampleMapPtr *map_ptr,
                                  uint8_t key,
                                  uint32_t sample_rate,
                                  const int16_t *sample_data_ptr,
                                  uint8_t channel,
                                  size_t num_samples,
                                  const struct KSynthSampleLoop *sample_loop);

/**
 * Frees the memory associated with a sample map.
 *
 * # Arguments
 * `map_ptr` - Pointer to the sample map to free.
 *
 * # Safety
 * `map_ptr` must be a valid pointer returned by `ksynth_sample_map_new`.
 * After calling this function, the `map_ptr` becomes invalid and must not be used again.
 */
void ksynth_sample_map_free(struct KSynthSampleMapPtr *ptr);

/**
 * Creates a new, empty sample map.
 *
 * The returned pointer represents a shared sample map and must be freed
 * using `ksynth_sample_map_free` when no longer needed.
 *
 * # Returns
 * A pointer to a new sample map instance, or `null` on failure.
 */
struct KSynthSampleMapPtr *ksynth_sample_map_new(void);

/**
 * Sets the fade-out sample (in sample units).
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 * `fade_out_sample` - The number of samples over which to apply the fade-out.
 *                     Set to 0 to disable fade-out (no fading).
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` previously returned by `ksynth_new`.
 */
void ksynth_set_fade_out_sample(struct KSynthPtr *synth_ptr, uint64_t fade_out_sample);

/**
 * Sets the maximum polyphony (number of voices the synthesizer can handle).
 *
 * If `max_polyphony` exceeds the system-supported maximum (as returned by `ksynth_get_max_supported_polyphony`),
 * it will be clamped to that upper limit (`MAX_POLYPHONY`).
 * Additionally, if `max_polyphony` is set to 0, it will be set as 1.
 *
 * This function also stops all active voices, removes inactive voices, and resets the current polyphony to match
 * the number of active voices.
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 * `max_polyphony` - The desired maximum number of voices.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` previously returned by `ksynth_new`.
 * If `max_polyphony` is set to 0, it will be set as 1.
 */
void ksynth_set_max_polyphony(struct KSynthPtr *synth_ptr,
                              uint32_t max_polyphony);

/**
 * Sets a new shared sample map for the synthesizer, replacing the existing one.
 *
 * # Arguments
 * `synth_ptr` - A pointer to the KSynth instance.
 * `sample_map_ptr` - A pointer to the new shared sample map.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth`.
 * `sample_map_ptr` must be a valid pointer returned by `ksynth_sample_map_new`.
 * This function clones the Arc, so the caller still needs to manage the lifetime of `sample_map_ptr` using `ksynth_sample_map_free`.
 */
void ksynth_set_samples(struct KSynthPtr *synth_ptr,
                        const struct KSynthSampleMapPtr *sample_map_ptr);

/**
 * Sets a new velocity curve for the KSynth instance.
 *
 * The velocity curve is an array of 128 `f32` values, where the index represents
 * the MIDI velocity (0-127) and the value is the corresponding amplitude scaling factor.
 * Values provided in `new_velocity_curve_ptr` that are outside the range [0.0, 1.0]
 * will be clamped to this range by the underlying synthesizer.
 *
 * # Arguments
 * `synth_ptr` - A pointer to the `KSynth` instance.
 * `new_velocity_curve_ptr` - A pointer to a C array of `float` (exactly 128 elements)
 *                              containing the new velocity curve data.
 *
 * # Returns
 * `true` (1) if the velocity curve was successfully set.
 * `false` (0) if `synth_ptr` or `new_velocity_curve_ptr` is null.
 *
 * # Safety
 * `synth_ptr` must be a valid pointer to a `KSynth` instance previously returned by `ksynth_new`.
 * `new_velocity_curve_ptr` must be a valid pointer to a readable block of memory
 *   containing `128` `f32` values.
 */
bool ksynth_set_velocity_curve(struct KSynthPtr *synth_ptr, const float *new_velocity_curve_ptr);

#ifdef __cplusplus
}  // extern "C"
#endif  // __cplusplus

#endif  /* ksynth_h */
