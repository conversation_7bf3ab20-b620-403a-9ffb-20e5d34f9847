/**
 * @file predefined_sample.h
 * @brief ピアノサウンドサンプル生成のためのヘッダーファイル
 */

#ifndef PREDEFINED_SAMPLE_H
#define PREDEFINED_SAMPLE_H

#include <stdint.h>
#include <stdlib.h>

/**
 * @brief ピアノサンプルを生成する関数
 * @param sample_rate サンプリングレート（Hz）
 * @param freq 基本周波数（Hz）
 * @param sample_count 生成するサンプル数
 * @param samples 生成したサンプルを格納する配列へのポインタ（事前に確保されていること）
 * @return 成功時は1、失敗時は0
 */
int generate_piano_sample(uint32_t sample_rate, float freq, size_t sample_count, int16_t* samples);

#endif /* PREDEFINED_SAMPLE_H */
