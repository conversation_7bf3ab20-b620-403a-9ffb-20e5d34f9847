#ifndef FAKE_BASSMIDI_H
#define FAKE_BASSMIDI_H

#include "bass.h"
#include <stdbool.h>
#include <stdint.h> // uint32_t用に追加

#ifdef _WIN32
#define FAKEBASSMIDI_API __declspec(dllexport)
#else
#define FAKEBASSMIDI_API
#endif

#ifdef __cplusplus
extern "C"
{
#endif

// MIDIキュー処理関数
/**
 * MIDIキューの初期化
 * @return 初期化に成功した場合はTRUE、失敗した場合はFALSE
 */
FAKEBASSMIDI_API BOOL __cdecl MIDI_QueueInit(void);

/**
 * MIDIキューの解放
 */
FAKEBASSMIDI_API void __cdecl MIDI_QueueFree(void);

/**
 * MIDIイベントをキューにプッシュする
 * @param event MIDI命令（32ビット整数にエンコードされたMIDIコマンド）
 * @return 成功した場合はTRUE、失敗した場合はFALSE
 */
FAKEBASSMIDI_API BOOL __cdecl MIDI_QueuePush(uint32_t event);

/**
 * MIDIイベントをキューからポップする
 * @param event MIDIイベントコードを格納する変数へのポインタ
 * @return 成功した場合はTRUE、キューが空の場合はFALSE
 */
FAKEBASSMIDI_API BOOL __cdecl MIDI_QueuePop(uint32_t* event);

/**
 * MIDIキュー内のイベント数を取得
 * @return キュー内のイベント数
 */
FAKEBASSMIDI_API uint32_t __cdecl MIDI_QueueCount(void);

/**
 * MIDIキューをクリア
 */
FAKEBASSMIDI_API void __cdecl MIDI_QueueClear(void);

#define HSOUNDFONT DWORD

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_StreamSetFonts(
        HSTREAM handle,
        void *fonts,
        DWORD count);

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_FontLoad(
        HSOUNDFONT font,
        int preset,
        int bank);

    FAKEBASSMIDI_API HSOUNDFONT __cdecl BASS_MIDI_FontInit(
        void *file,
        DWORD flags);

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_FontFree(
        HSOUNDFONT font);

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_StreamEvent(
        HSTREAM handle,
        DWORD chan,
        DWORD event,
        DWORD param);

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_StreamEvents(
        HSTREAM handle,
        DWORD mode,
        void *events,
        DWORD length);
    FAKEBASSMIDI_API HSTREAM __cdecl BASS_MIDI_StreamCreate(
        DWORD channels,
        DWORD flags,
        DWORD freq);

    FAKEBASSMIDI_API void __cdecl BASS_MIDI_Free();

#ifdef __cplusplus
}
#endif

#endif
