#ifndef FAKE_BASSMIDI_H
#define FAKE_BASSMIDI_H

#include "bass.h"
#include <stdbool.h>
#include <stdint.h> // uint32_t用に追加

#ifdef _WIN32
#define FAKEBASSMIDI_API __declspec(dllexport)
#else
#define FAKEBASSMIDI_API
#endif

#ifdef __cplusplus
extern "C"
{
#endif

// MIDIキュー処理関数
/**
 * MIDIキューの初期化
 * @return 初期化に成功した場合はTRUE、失敗した場合はFALSE
 */
FAKEBASSMIDI_API BOOL __cdecl MIDI_QueueInit(void);

/**
 * MIDIキューの解放
 */
FAKEBASSMIDI_API void __cdecl MIDI_QueueFree(void);

/**
 * MIDIイベントをキューにプッシュする
 * @param event MIDI命令（32ビット整数にエンコードされたMIDIコマンド）
 * @return 成功した場合はTRUE、失敗した場合はFALSE
 */
FAKEBASSMIDI_API BOOL __cdecl MIDI_QueuePush(uint32_t event);

/**
 * MIDIイベントをキューからポップする
 * @param event MIDIイベントコードを格納する変数へのポインタ
 * @return 成功した場合はTRUE、キューが空の場合はFALSE
 */
FAKEBASSMIDI_API BOOL __cdecl MIDI_QueuePop(uint32_t* event);

/**
 * MIDIキュー内のイベント数を取得
 * @return キュー内のイベント数
 */
FAKEBASSMIDI_API uint32_t __cdecl MIDI_QueueCount(void);

/**
 * MIDIキューをクリア
 */
FAKEBASSMIDI_API void __cdecl MIDI_QueueClear(void);

#define HSOUNDFONT DWORD

// MIDI stream flags
#define BASS_MIDI_DECAYEND 0x1000
#define BASS_MIDI_NOFX 0x2000
#define BASS_MIDI_DECAYSEEK 0x4000
#define BASS_MIDI_NOCROP 0x8000
#define BASS_MIDI_NOTEOFF1 0x10000
#define BASS_MIDI_SINCINTER 0x800000

// MIDI events mode flags
#define BASS_MIDI_EVENTS_STRUCT 0            // BASS_MIDI_EVENT structures
#define BASS_MIDI_EVENTS_RAW 0x10000         // raw MIDI event data
#define BASS_MIDI_EVENTS_SYNC 0x1000000      // flag: trigger event syncs
#define BASS_MIDI_EVENTS_NORSTATUS 0x2000000 // flag: no running status
#define BASS_MIDI_EVENTS_CANCEL 0x4000000    // flag: cancel pending events
#define BASS_MIDI_EVENTS_TIME 0x8000000      // flag: delta-time info is present
#define BASS_MIDI_EVENTS_ABSTIME 0x10000000  // flag: absolute time info is present
#define BASS_MIDI_EVENTS_ASYNC 0x20000000    // flag: process asynchronously
#define BASS_MIDI_EVENTS_FILTER 0x40000000   // flag: apply filtering

// MIDI event structure
typedef struct {
    DWORD event;    // MIDI event
    DWORD param;    // event parameter
    DWORD chan;     // channel
    DWORD tick;     // tick (if BASS_MIDI_EVENTS_TIME is used)
    DWORD pos;      // byte position (if BASS_MIDI_EVENTS_ABSTIME is used)
} BASS_MIDI_EVENT;

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_StreamSetFonts(
        HSTREAM handle,
        void *fonts,
        DWORD count);

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_FontLoad(
        HSOUNDFONT font,
        int preset,
        int bank);

    FAKEBASSMIDI_API HSOUNDFONT __cdecl BASS_MIDI_FontInit(
        void *file,
        DWORD flags);

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_FontFree(
        HSOUNDFONT font);

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_StreamEvent(
        HSTREAM handle,
        DWORD chan,
        DWORD event,
        DWORD param);

    FAKEBASSMIDI_API BOOL __cdecl BASS_MIDI_StreamEvents(
        HSTREAM handle,
        DWORD mode,
        void *events,
        DWORD length);
    FAKEBASSMIDI_API HSTREAM __cdecl BASS_MIDI_StreamCreate(
        DWORD channels,
        DWORD flags,
        DWORD freq);

    FAKEBASSMIDI_API HSTREAM __cdecl BASS_MIDI_StreamCreateFile(
        BOOL mem,
        void *file,
        DWORD offset,
        DWORD length,
        DWORD flags,
        DWORD freq);

    FAKEBASSMIDI_API HSTREAM __cdecl BASS_MIDI_StreamCreateURL(
        char *url,
        DWORD offset,
        DWORD flags,
        void *proc,
        void *user,
        DWORD freq);

    FAKEBASSMIDI_API HSTREAM __cdecl BASS_MIDI_StreamCreateFileUser(
        DWORD system,
        DWORD flags,
        void *procs,
        void *user,
        DWORD freq);

    FAKEBASSMIDI_API void __cdecl BASS_MIDI_Free();

#ifdef __cplusplus
}
#endif

#endif
