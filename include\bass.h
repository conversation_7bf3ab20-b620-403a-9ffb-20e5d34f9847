#ifndef FAKE_BASS_H
#define FAKE_BASS_H

#include <stdbool.h>

#ifdef _WIN32
#define FAKEBASS_API __declspec(dllexport)
#else
#define FAKEBASS_API
#endif

#ifdef __cplusplus
extern "C"
{
#endif

#define BOOL int
#define TRUE 1
#define FALSE 0
#define DWORD unsigned long
#define HWND void *

#define HSTREAM DWORD

    FAKEBASS_API BOOL BASS_Init(
        int device,
        DWORD freq,
        DWORD flags,
        HWND win,
        void *clsid);

    FAKEBASS_API BOOL BASS_Free(void);

    FAKEBASS_API BOOL BASS_ChannelSetAttribute(
        DWORD handle,
        DWORD attrib,
        float value);

    FAKEBASS_API DWORD BASS_ChannelFlags(
        DWORD handle,
        DWORD flags,
        DWORD mask);
    FAKEBASS_API DWORD BASS_ChannelGetData(
        HSTREAM handle,
        void *buffer,
        DWORD length);

    FAKEBASS_API BOOL BASS_StreamFree(
        HSTREAM handle);

// Some useful flags for BASS_ChannelGetData
#define BASS_DATA_AVAILABLE 0
#define BASS_DATA_FLOAT 0x40000000

// Channel attributes
#define BASS_ATTRIB_MIDI_VOICES 0x12003 // Number of MIDI voices to use

    // Callback function type for attribute notifications
    typedef void (*BASSATTRIBCALLBACK)(DWORD handle, DWORD attrib, float value, void *user);

#ifdef __cplusplus
}
#endif

#endif
