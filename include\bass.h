#ifndef FAKE_BASS_H
#define FAKE_BASS_H

#include <stdbool.h>

#ifdef _WIN32
#define FAKEBASS_API __declspec(dllexport)
#else
#define FAKEBASS_API
#endif

#ifdef __cplusplus
extern "C"
{
#endif

#define BOOL int
#define TRUE 1
#define FALSE 0
#define DWORD unsigned long
#define HWND void *

#define HSTREAM DWORD

    FAKEBASS_API BOOL BASS_Init(
        int device,
        DWORD freq,
        DWORD flags,
        HW<PERSON> win,
        void *clsid);

    FAKEBASS_API BOOL BASS_Free(void);

    FAKEBASS_API BOOL BASS_SetConfig(
        DWORD option,
        DWORD value);

    FAKEBASS_API DWORD BASS_GetConfig(
        DWORD option);

    FAKEBASS_API BOOL BASS_ChannelSetAttribute(
        DWORD handle,
        DWORD attrib,
        float value);

    FAKEBASS_API DWORD BASS_ChannelFlags(
        DWORD handle,
        DWORD flags,
        DWORD mask);
    FAKEBASS_API DWORD BASS_ChannelGetData(
        HSTREAM handle,
        void *buffer,
        DWORD length);

    FAKEBASS_API BOOL BASS_StreamFree(
        HSTREAM handle);

// Some useful flags for BASS_ChannelGetData
#define BASS_DATA_AVAILABLE 0
#define BASS_DATA_FLOAT 0x40000000

// Configuration options for BASS_SetConfig
#define BASS_CONFIG_BUFFER 0
#define BASS_CONFIG_UPDATEPERIOD 1
#define BASS_CONFIG_GVOL_SAMPLE 4
#define BASS_CONFIG_GVOL_STREAM 5
#define BASS_CONFIG_GVOL_MUSIC 6
#define BASS_CONFIG_CURVE_VOL 7
#define BASS_CONFIG_CURVE_PAN 8
#define BASS_CONFIG_FLOATDSP 9
#define BASS_CONFIG_3DALGORITHM 10
#define BASS_CONFIG_NET_TIMEOUT 11
#define BASS_CONFIG_NET_BUFFER 12
#define BASS_CONFIG_PAUSE_NOPLAY 13
#define BASS_CONFIG_NET_PREBUF 15
#define BASS_CONFIG_NET_PASSIVE 18
#define BASS_CONFIG_REC_BUFFER 19
#define BASS_CONFIG_NET_PLAYLIST 21
#define BASS_CONFIG_MUSIC_VIRTUAL 22
#define BASS_CONFIG_VERIFY 23
#define BASS_CONFIG_UPDATETHREADS 24
#define BASS_CONFIG_DEV_BUFFER 27
#define BASS_CONFIG_VISTA_TRUEPOS 30
#define BASS_CONFIG_IOS_SESSION 34
#define BASS_CONFIG_IOS_MIXAUDIO 34
#define BASS_CONFIG_DEV_DEFAULT 36
#define BASS_CONFIG_NET_READTIMEOUT 37
#define BASS_CONFIG_VISTA_SPEAKERS 38
#define BASS_CONFIG_IOS_SPEAKER 39
#define BASS_CONFIG_MF_DISABLE 40
#define BASS_CONFIG_HANDLES 41
#define BASS_CONFIG_UNICODE 42
#define BASS_CONFIG_SRC 43
#define BASS_CONFIG_SRC_SAMPLE 44
#define BASS_CONFIG_ASYNCFILE_BUFFER 45
#define BASS_CONFIG_OGG_PRESCAN 47
#define BASS_CONFIG_MF_VIDEO 48
#define BASS_CONFIG_AIRPLAY 49
#define BASS_CONFIG_DEV_NONSTOP 50
#define BASS_CONFIG_IOS_NOCATEGORY 51
#define BASS_CONFIG_VERIFY_NET 52
#define BASS_CONFIG_DEV_PERIOD 53
#define BASS_CONFIG_FLOAT 54
#define BASS_CONFIG_NET_SEEK 56

// Channel attributes
#define BASS_ATTRIB_MIDI_VOICES 0x12003 // Number of MIDI voices to use

    // Callback function type for attribute notifications
    typedef void (*BASSATTRIBCALLBACK)(DWORD handle, DWORD attrib, float value, void *user);

#ifdef __cplusplus
}
#endif

#endif
