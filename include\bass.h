#ifndef FAKE_BASS_H
#define FAKE_BASS_H

#include <stdbool.h>

#ifdef _WIN32
#define FAKEBASS_API __declspec(dllexport)
#else
#define FAKEBASS_API
#endif

#ifdef __cplusplus
extern "C"
{
#endif

#define BOOL int
#define TRUE 1
#define FALSE 0
#define DWORD unsigned long
#define HWND void *

#define HSTREAM DWORD

    FAKEBASS_API BOOL BASS_Init(
        int device,
        DWORD freq,
        DWORD flags,
        HW<PERSON> win,
        void *clsid);

    FAKEBASS_API BOOL BASS_Free(void);

    FAKEBASS_API BOOL BASS_SetConfig(
        DWORD option,
        DWORD value);

    FAKEBASS_API DWORD BASS_GetConfig(
        DWORD option);

    FAKEBASS_API DWORD BASS_GetVersion(void);

    FAKEBASS_API int BASS_ErrorGetCode(void);

    FAKEBASS_API BOOL BASS_GetDeviceInfo(
        DWORD device,
        void *info);

    FAKEBASS_API BOOL BASS_SetDevice(
        DWORD device);

    FAKEBASS_API DWORD BASS_GetDevice(void);

    FAKEBASS_API BOOL BASS_GetInfo(
        void *info);

    FAKEBASS_API BOOL BASS_Update(
        DWORD length);

    FAKEBASS_API float BASS_GetCPU(void);

    FAKEBASS_API BOOL BASS_Start(void);

    FAKEBASS_API BOOL BASS_Stop(void);

    FAKEBASS_API BOOL BASS_Pause(void);

    FAKEBASS_API DWORD BASS_GetVolume(void);

    FAKEBASS_API BOOL BASS_SetVolume(
        float volume);

    // Sample functions
    FAKEBASS_API DWORD BASS_SampleLoad(
        BOOL mem,
        void *file,
        DWORD offset,
        DWORD length,
        DWORD max,
        DWORD flags);

    FAKEBASS_API BOOL BASS_SampleFree(
        DWORD handle);

    FAKEBASS_API DWORD BASS_SampleGetChannel(
        DWORD handle,
        BOOL onlynew);

    FAKEBASS_API BOOL BASS_SampleGetInfo(
        DWORD handle,
        void *info);

    FAKEBASS_API BOOL BASS_SampleSetInfo(
        DWORD handle,
        void *info);

    FAKEBASS_API BOOL BASS_SampleStop(
        DWORD handle);

    // Stream functions
    FAKEBASS_API HSTREAM BASS_StreamCreate(
        DWORD freq,
        DWORD chans,
        DWORD flags,
        void *proc,
        void *user);

    FAKEBASS_API HSTREAM BASS_StreamCreateFile(
        BOOL mem,
        void *file,
        DWORD offset,
        DWORD length,
        DWORD flags);

    FAKEBASS_API HSTREAM BASS_StreamCreateURL(
        char *url,
        DWORD offset,
        DWORD flags,
        void *proc,
        void *user);

    FAKEBASS_API BOOL BASS_ChannelSetAttribute(
        DWORD handle,
        DWORD attrib,
        float value);

    FAKEBASS_API BOOL BASS_ChannelGetAttribute(
        DWORD handle,
        DWORD attrib,
        float *value);

    FAKEBASS_API DWORD BASS_ChannelFlags(
        DWORD handle,
        DWORD flags,
        DWORD mask);

    FAKEBASS_API DWORD BASS_ChannelGetData(
        HSTREAM handle,
        void *buffer,
        DWORD length);

    FAKEBASS_API BOOL BASS_ChannelGetInfo(
        DWORD handle,
        void *info);

    FAKEBASS_API DWORD BASS_ChannelGetLength(
        DWORD handle,
        DWORD mode);

    FAKEBASS_API DWORD BASS_ChannelGetPosition(
        DWORD handle,
        DWORD mode);

    FAKEBASS_API BOOL BASS_ChannelSetPosition(
        DWORD handle,
        DWORD pos,
        DWORD mode);

    FAKEBASS_API BOOL BASS_ChannelPlay(
        DWORD handle,
        BOOL restart);

    FAKEBASS_API BOOL BASS_ChannelStop(
        DWORD handle);

    FAKEBASS_API BOOL BASS_ChannelPause(
        DWORD handle);

    FAKEBASS_API DWORD BASS_ChannelIsActive(
        DWORD handle);

    FAKEBASS_API BOOL BASS_ChannelUpdate(
        DWORD handle,
        DWORD length);

    FAKEBASS_API DWORD BASS_ChannelBytes2Seconds(
        DWORD handle,
        DWORD pos);

    FAKEBASS_API DWORD BASS_ChannelSeconds2Bytes(
        DWORD handle,
        double pos);

    FAKEBASS_API DWORD BASS_ChannelGetLevel(
        DWORD handle);

    FAKEBASS_API BOOL BASS_ChannelGetLevelEx(
        DWORD handle,
        float *levels,
        float length,
        DWORD flags);

    FAKEBASS_API DWORD BASS_ChannelSetSync(
        DWORD handle,
        DWORD type,
        DWORD param,
        void *proc,
        void *user);

    FAKEBASS_API BOOL BASS_ChannelRemoveSync(
        DWORD handle,
        DWORD sync);

    FAKEBASS_API DWORD BASS_ChannelSetDSP(
        DWORD handle,
        void *proc,
        void *user,
        int priority);

    FAKEBASS_API BOOL BASS_ChannelRemoveDSP(
        DWORD handle,
        DWORD dsp);

    FAKEBASS_API BOOL BASS_ChannelSetLink(
        DWORD handle,
        DWORD chan);

    FAKEBASS_API BOOL BASS_ChannelRemoveLink(
        DWORD handle,
        DWORD chan);

    FAKEBASS_API BOOL BASS_ChannelSet3DAttributes(
        DWORD handle,
        int mode,
        float min,
        float max,
        int iangle,
        int oangle,
        float outvol);

    FAKEBASS_API BOOL BASS_ChannelGet3DAttributes(
        DWORD handle,
        DWORD *mode,
        float *min,
        float *max,
        DWORD *iangle,
        DWORD *oangle,
        float *outvol);

    FAKEBASS_API BOOL BASS_ChannelSet3DPosition(
        DWORD handle,
        void *pos,
        void *orient,
        void *vel);

    FAKEBASS_API BOOL BASS_ChannelGet3DPosition(
        DWORD handle,
        void *pos,
        void *orient,
        void *vel);

    FAKEBASS_API BOOL BASS_StreamFree(
        HSTREAM handle);

// Error codes
#define BASS_OK 0
#define BASS_ERROR_MEM 1
#define BASS_ERROR_FILEOPEN 2
#define BASS_ERROR_DRIVER 3
#define BASS_ERROR_BUFLOST 4
#define BASS_ERROR_HANDLE 5
#define BASS_ERROR_FORMAT 6
#define BASS_ERROR_POSITION 7
#define BASS_ERROR_INIT 8
#define BASS_ERROR_START 9
#define BASS_ERROR_SSL 10
#define BASS_ERROR_ALREADY 14
#define BASS_ERROR_NOTAUDIO 17
#define BASS_ERROR_NOCHAN 18
#define BASS_ERROR_ILLTYPE 19
#define BASS_ERROR_ILLPARAM 20
#define BASS_ERROR_NO3D 21
#define BASS_ERROR_NOEAX 22
#define BASS_ERROR_DEVICE 23
#define BASS_ERROR_NOPLAY 24
#define BASS_ERROR_FREQ 25
#define BASS_ERROR_NOTFILE 27
#define BASS_ERROR_NOHW 29
#define BASS_ERROR_EMPTY 31
#define BASS_ERROR_NONET 32
#define BASS_ERROR_CREATE 33
#define BASS_ERROR_NOFX 34
#define BASS_ERROR_NOTAVAIL 37
#define BASS_ERROR_DECODE 38
#define BASS_ERROR_DX 39
#define BASS_ERROR_TIMEOUT 40
#define BASS_ERROR_FILEFORM 41
#define BASS_ERROR_SPEAKER 42
#define BASS_ERROR_VERSION 43
#define BASS_ERROR_CODEC 44
#define BASS_ERROR_ENDED 45
#define BASS_ERROR_BUSY 46
#define BASS_ERROR_UNSTREAMABLE 47

// BASS_Init flags
#define BASS_DEVICE_8BITS 1
#define BASS_DEVICE_MONO 2
#define BASS_DEVICE_3D 4
#define BASS_DEVICE_16BITS 8
#define BASS_DEVICE_LATENCY 0x100
#define BASS_DEVICE_CPSPEAKERS 0x400
#define BASS_DEVICE_SPEAKERS 0x800
#define BASS_DEVICE_NOSPEAKER 0x1000
#define BASS_DEVICE_DMIX 0x2000
#define BASS_DEVICE_FREQ 0x4000
#define BASS_DEVICE_STEREO 0x8000
#define BASS_DEVICE_HOG 0x10000
#define BASS_DEVICE_AUDIOTRACK 0x20000
#define BASS_DEVICE_DSOUND 0x40000

// Stream/Sample/Music flags
#define BASS_SAMPLE_8BITS 1
#define BASS_SAMPLE_FLOAT 256
#define BASS_SAMPLE_MONO 2
#define BASS_SAMPLE_LOOP 4
#define BASS_SAMPLE_3D 8
#define BASS_SAMPLE_SOFTWARE 16
#define BASS_SAMPLE_MUTEMAX 32
#define BASS_SAMPLE_VAM 64
#define BASS_SAMPLE_FX 128
#define BASS_SAMPLE_OVER_VOL 0x10000
#define BASS_SAMPLE_OVER_POS 0x20000
#define BASS_SAMPLE_OVER_DIST 0x30000

#define BASS_STREAM_PRESCAN 0x20000
#define BASS_STREAM_AUTOFREE 0x40000
#define BASS_STREAM_RESTRATE 0x80000
#define BASS_STREAM_BLOCK 0x100000
#define BASS_STREAM_DECODE 0x200000
#define BASS_STREAM_STATUS 0x800000

// Channel status
#define BASS_ACTIVE_STOPPED 0
#define BASS_ACTIVE_PLAYING 1
#define BASS_ACTIVE_STALLED 2
#define BASS_ACTIVE_PAUSED 3
#define BASS_ACTIVE_PAUSED_DEVICE 4

// Position modes
#define BASS_POS_BYTE 0
#define BASS_POS_MUSIC_ORDER 1
#define BASS_POS_OGG 3
#define BASS_POS_INEXACT 0x8000000
#define BASS_POS_DECODE 0x10000000
#define BASS_POS_DECODETO 0x20000000
#define BASS_POS_SCAN 0x40000000

// Some useful flags for BASS_ChannelGetData
#define BASS_DATA_AVAILABLE 0
#define BASS_DATA_NOREMOVE 0x10000000
#define BASS_DATA_FLOAT 0x40000000
#define BASS_DATA_FFT256 0x80000000
#define BASS_DATA_FFT512 0x80000001
#define BASS_DATA_FFT1024 0x80000002
#define BASS_DATA_FFT2048 0x80000003
#define BASS_DATA_FFT4096 0x80000004
#define BASS_DATA_FFT8192 0x80000005
#define BASS_DATA_FFT16384 0x80000006
#define BASS_DATA_FFT32768 0x80000007
#define BASS_DATA_FFT_INDIVIDUAL 0x10
#define BASS_DATA_FFT_NOWINDOW 0x20
#define BASS_DATA_FFT_REMOVEDC 0x40
#define BASS_DATA_FFT_COMPLEX 0x80
#define BASS_DATA_FFT_NYQUIST 0x100

// Configuration options for BASS_SetConfig
#define BASS_CONFIG_BUFFER 0
#define BASS_CONFIG_UPDATEPERIOD 1
#define BASS_CONFIG_GVOL_SAMPLE 4
#define BASS_CONFIG_GVOL_STREAM 5
#define BASS_CONFIG_GVOL_MUSIC 6
#define BASS_CONFIG_CURVE_VOL 7
#define BASS_CONFIG_CURVE_PAN 8
#define BASS_CONFIG_FLOATDSP 9
#define BASS_CONFIG_3DALGORITHM 10
#define BASS_CONFIG_NET_TIMEOUT 11
#define BASS_CONFIG_NET_BUFFER 12
#define BASS_CONFIG_PAUSE_NOPLAY 13
#define BASS_CONFIG_NET_PREBUF 15
#define BASS_CONFIG_NET_PASSIVE 18
#define BASS_CONFIG_REC_BUFFER 19
#define BASS_CONFIG_NET_PLAYLIST 21
#define BASS_CONFIG_MUSIC_VIRTUAL 22
#define BASS_CONFIG_VERIFY 23
#define BASS_CONFIG_UPDATETHREADS 24
#define BASS_CONFIG_DEV_BUFFER 27
#define BASS_CONFIG_VISTA_TRUEPOS 30
#define BASS_CONFIG_IOS_SESSION 34
#define BASS_CONFIG_IOS_MIXAUDIO 34
#define BASS_CONFIG_DEV_DEFAULT 36
#define BASS_CONFIG_NET_READTIMEOUT 37
#define BASS_CONFIG_VISTA_SPEAKERS 38
#define BASS_CONFIG_IOS_SPEAKER 39
#define BASS_CONFIG_MF_DISABLE 40
#define BASS_CONFIG_HANDLES 41
#define BASS_CONFIG_UNICODE 42
#define BASS_CONFIG_SRC 43
#define BASS_CONFIG_SRC_SAMPLE 44
#define BASS_CONFIG_ASYNCFILE_BUFFER 45
#define BASS_CONFIG_OGG_PRESCAN 47
#define BASS_CONFIG_MF_VIDEO 48
#define BASS_CONFIG_AIRPLAY 49
#define BASS_CONFIG_DEV_NONSTOP 50
#define BASS_CONFIG_IOS_NOCATEGORY 51
#define BASS_CONFIG_VERIFY_NET 52
#define BASS_CONFIG_DEV_PERIOD 53
#define BASS_CONFIG_FLOAT 54
#define BASS_CONFIG_NET_SEEK 56

// Channel attributes
#define BASS_ATTRIB_FREQ 1
#define BASS_ATTRIB_VOL 2
#define BASS_ATTRIB_PAN 3
#define BASS_ATTRIB_EAXMIX 4
#define BASS_ATTRIB_NOBUFFER 5
#define BASS_ATTRIB_VBR 6
#define BASS_ATTRIB_CPU 7
#define BASS_ATTRIB_SRC 8
#define BASS_ATTRIB_NET_RESUME 9
#define BASS_ATTRIB_SCANINFO 10
#define BASS_ATTRIB_NORAMP 11
#define BASS_ATTRIB_BITRATE 12
#define BASS_ATTRIB_BUFFER 13
#define BASS_ATTRIB_GRANULE 14
#define BASS_ATTRIB_USER 15
#define BASS_ATTRIB_TAIL 16
#define BASS_ATTRIB_PUSH_LIMIT 17
#define BASS_ATTRIB_DOWNLOADPROC 18
#define BASS_ATTRIB_VOLDSP 19
#define BASS_ATTRIB_VOLDSP_PRIORITY 20
#define BASS_ATTRIB_MUSIC_AMPLIFY 0x100
#define BASS_ATTRIB_MUSIC_PANSEP 0x101
#define BASS_ATTRIB_MUSIC_PSCALER 0x102
#define BASS_ATTRIB_MUSIC_BPM 0x103
#define BASS_ATTRIB_MUSIC_SPEED 0x104
#define BASS_ATTRIB_MUSIC_VOL_GLOBAL 0x105
#define BASS_ATTRIB_MUSIC_ACTIVE 0x106
#define BASS_ATTRIB_MUSIC_VOL_CHAN 0x200
#define BASS_ATTRIB_MUSIC_VOL_INST 0x300
#define BASS_ATTRIB_MIDI_VOICES 0x12003
#define BASS_ATTRIB_MIDI_CHANS 0x12001
#define BASS_ATTRIB_MIDI_TRACK_VOL 0x12100

// Sync types
#define BASS_SYNC_POS 0
#define BASS_SYNC_END 2
#define BASS_SYNC_META 4
#define BASS_SYNC_SLIDE 5
#define BASS_SYNC_STALL 6
#define BASS_SYNC_DOWNLOAD 7
#define BASS_SYNC_FREE 8
#define BASS_SYNC_SETPOS 11
#define BASS_SYNC_MUSICPOS 10
#define BASS_SYNC_MUSICINST 1
#define BASS_SYNC_MUSICFX 3
#define BASS_SYNC_OGG_CHANGE 12
#define BASS_SYNC_DEV_FAIL 14
#define BASS_SYNC_DEV_FORMAT 15
#define BASS_SYNC_THREAD 0x20000000
#define BASS_SYNC_MIXTIME 0x40000000
#define BASS_SYNC_ONETIME 0x80000000

// 3D channel modes
#define BASS_3DMODE_NORMAL 0
#define BASS_3DMODE_RELATIVE 1
#define BASS_3DMODE_OFF 2

    // Callback function types
    typedef void (*BASSATTRIBCALLBACK)(DWORD handle, DWORD attrib, float value, void *user);
    typedef void (*SYNCPROC)(DWORD handle, DWORD channel, DWORD data, void *user);
    typedef void (*DSPPROC)(DWORD handle, DWORD channel, void *buffer, DWORD length, void *user);
    typedef DWORD (*STREAMPROC)(DWORD handle, void *buffer, DWORD length, void *user);

#ifdef __cplusplus
}
#endif

#endif
