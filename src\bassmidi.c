#define _USE_MATH_DEFINES
#include <windows.h> // for Windows API - インクルードを最初に移動
#include "bassmidi.h"
#include "event.h"
#include "mutex.h" // ミューテックス機能のインクルード
#include <stdio.h>
#include <string.h> // for strcmp
#include <stdlib.h> // for malloc, free
#include <math.h>   // for sin(), M_PI
#include <stdint.h> // for uint32_t
#include <time.h>   // for time functions
#include "ksynth.h"
#include "predefined_sample.h"

// 関数の前方宣言
static BOOL add_to_midi_buffer(uint32_t midiData);
static void process_midi_buffer(struct KSynthPtr *synth);
void KSynthSendMIDI(struct KSynthPtr *synth, uint32_t midiData);
static void process_all_midi_events(struct KSynthPtr *synth);

// バッファサイズの定義
#define MAX_MIDI_BUFFER_SIZE 4096

// MIDI イベント関連の定数定義
#define BASS_MIDI_EVENTS_STRUCT 0            // BASS_MIDI_EVENT structures
#define BASS_MIDI_EVENTS_RAW 0x10000         // raw MIDI event data
#define BASS_MIDI_EVENTS_SYNC 0x1000000      // flag: trigger event syncs
#define BASS_MIDI_EVENTS_NORSTATUS 0x2000000 // flag: no running status
#define BASS_MIDI_EVENTS_CANCEL 0x4000000    // flag: cancel pending events
#define BASS_MIDI_EVENTS_TIME 0x8000000      // flag: delta-time info is present
#define BASS_MIDI_EVENTS_ABSTIME 0x10000000  // flag: absolute time info is present
#define BASS_MIDI_EVENTS_ASYNC 0x20000000    // flag: process asynchronously
#define BASS_MIDI_EVENTS_FILTER 0x40000000   // flag: apply filtering
#define BASS_MIDI_EVENTS_FLUSH 0x80000000    // flag: flush async events

// 元のBASSMIDIライブラリに合わせたMIDI_EVENT構造体
typedef struct
{
    DWORD event; // MIDI event (MIDI_EVENT_xxx)
    DWORD param; // Event parameter
    DWORD chan;  // Channel
    DWORD tick;  // Tick position (relative to the start)
    DWORD pos;   // Byte position (relative to the start)
} MIDI_EVENT;

// DO NOT TOUCH THIS CODE
// IF YOU TOUCH THIS CODE, YOU WILL BE FIRED
static uint32_t *g_midiEventBuffer;
static volatile uint32_t g_midiBufferSize = 0; // 現在のバッファ内のイベント数
static volatile LONG g_midiBufferLock = 0;     // バッファ用のスピンロック
// DO NOT TOUCH THIS CODE END

// MIDIキュー関連の定義
#define MIDI_QUEUE_SIZE MAX_MIDI_BUFFER_SIZE

// キューの読み書き位置
static volatile uint32_t g_midiQueueReadPos = 0;
static volatile uint32_t g_midiQueueWritePos = 0;

/**
 * MIDIキューの初期化
 * @return 初期化に成功した場合はTRUE、失敗した場合はFALSE
 */
BOOL MIDI_QueueInit(void)
{
    // 既に初期化されている場合は何もしない
    if (g_midiEventBuffer != NULL)
    {
        return TRUE;
    }

    // MIDIイベントバッファのメモリ確保
    g_midiEventBuffer = (uint32_t *)malloc(MIDI_QUEUE_SIZE * sizeof(uint32_t));
    if (g_midiEventBuffer == NULL)
    {
        return FALSE;
    }

    // 初期化
    g_midiBufferSize = 0;
    g_midiQueueReadPos = 0;
    g_midiQueueWritePos = 0;

    return TRUE;
}

/**
 * MIDIキューの解放
 */
void MIDI_QueueFree(void)
{
    if (g_midiEventBuffer)
    {
        free(g_midiEventBuffer);
        g_midiEventBuffer = NULL;
    }
    g_midiBufferSize = 0;
    g_midiQueueReadPos = 0;
    g_midiQueueWritePos = 0;
}

/**
 * MIDIイベントをキューにプッシュする
 * @param event MIDIイベント
 * @return 成功した場合はTRUE、失敗した場合はFALSE
 */
BOOL MIDI_QueuePush(uint32_t event)
{
    // バッファが満杯の場合は追加できない
    if (g_midiBufferSize >= MIDI_QUEUE_SIZE)
    {
        return FALSE;
    }

    // スピンロック取得
    while (InterlockedExchange((LONG *)&g_midiBufferLock, 1) != 0)
    {
        // 他のスレッドがロックを持っている間は待機
        Sleep(0);
    }

    // キューにイベントを追加
    uint32_t writeIndex = g_midiQueueWritePos;
    g_midiEventBuffer[writeIndex] = event;

    // 書き込み位置を更新（循環バッファ）
    g_midiQueueWritePos = (g_midiQueueWritePos + 1) % MIDI_QUEUE_SIZE; // バッファ内のイベント数をインクリメント
    InterlockedIncrement((LONG *)&g_midiBufferSize);

    // スピンロック解放
    InterlockedExchange((LONG *)&g_midiBufferLock, 0);

    return TRUE;
}

/**
 * MIDIイベントをキューからポップする
 * @param event イベントコードを格納する変数へのポインタ
 * @param param パラメータを格納する変数へのポインタ
 * @param channel チャンネルを格納する変数へのポインタ
 * @param time 時間を格納する変数へのポインタ
 * @return 成功した場合はTRUE、キューが空の場合はFALSE
 */
BOOL MIDI_QueuePop(uint32_t *event)
{
    // バッファが空の場合は取得できない
    if (g_midiBufferSize == 0)
    {
        return FALSE;
    }

    // スピンロック取得
    while (InterlockedExchange((LONG *)&g_midiBufferLock, 1) != 0)
    {
        // 他のスレッドがロックを持っている間は待機
        Sleep(0);
    }

    // キューからイベントを取得
    uint32_t readIndex = g_midiQueueReadPos;
    *event = g_midiEventBuffer[readIndex];
    // 読み取り位置を更新（循環バッファ）
    g_midiQueueReadPos = (g_midiQueueReadPos + 1) % MIDI_QUEUE_SIZE;
    // バッファ内のイベント数をデクリメント
    InterlockedDecrement((LONG *)&g_midiBufferSize);

    // スピンロック解放
    InterlockedExchange((LONG *)&g_midiBufferLock, 0);

    return TRUE;
}

/**
 * MIDIキュー内のイベント数を取得
 * @return キュー内のイベント数
 */
uint32_t MIDI_QueueCount(void)
{
    return g_midiBufferSize;
}

/**
 * MIDIキューをクリア
 */
void MIDI_QueueClear(void)
{
    // スピンロック取得
    while (InterlockedExchange((LONG *)&g_midiBufferLock, 1) != 0)
    {
        // 他のスレッドがロックを持っている間は待機
        Sleep(0);
    }

    // キューをリセット
    g_midiQueueReadPos = 0;
    g_midiQueueWritePos = 0;
    g_midiBufferSize = 0;

    // スピンロック解放
    InterlockedExchange((LONG *)&g_midiBufferLock, 0);
}

// Configuration structure
typedef struct
{
    DWORD sampleRate;
    int numChannels;
    BOOL initialized;
} Config;

// Global configuration
static Config g_config = {0};

// MIDI stream management global variables
typedef struct
{
    HSTREAM handle;          // Stream handle
    BOOL active;             // Active flag
    float sampleRate;        // Sample rate
    int numChannels;         // Number of channels
    struct KSynthPtr *synth; // Pointer to the synthesizer instance
    Mutex synthMutex;        // KSynthインスタンスへのアクセスを保護するためのミューテックス
} MIDIStreamState;

#define MAX_MIDI_STREAMS 16
static MIDIStreamState g_midiStreams[MAX_MIDI_STREAMS] = {0};

// Forward declaration of functions
static void BASSMIDI_EventCallback(const char *eventName, void *eventData);
static void handle_free_stream_event(const char *eventName, void *eventData);
static MIDIStreamState *find_midi_stream(HSTREAM handle);
static HSTREAM add_midi_stream(float sampleRate, int numChannels);

FAKEBASSMIDI_API BOOL BASS_MIDI_StreamSetFonts(
    HSTREAM handle,
    void *fonts,
    DWORD count)
{
    // Dummy implementation, does nothing
    return TRUE;
}

FAKEBASSMIDI_API BOOL BASS_MIDI_FontLoad(
    HSOUNDFONT font,
    int preset,
    int bank)
{
    // Dummy implementation, does nothing
    return TRUE;
}

FAKEBASSMIDI_API HSOUNDFONT BASS_MIDI_FontInit(
    void *file,
    DWORD flags)
{
    // Dummy implementation, does nothing, just return always succesful font handle (1)
    return 1;
}

FAKEBASSMIDI_API BOOL BASS_MIDI_FontFree(
    HSOUNDFONT font)
{
    // Dummy implementation, does nothing
    return TRUE;
}

FAKEBASSMIDI_API BOOL BASS_MIDI_StreamEvent(
    HSTREAM handle,
    DWORD chan,
    DWORD event,
    DWORD param)
{
    return TRUE;
}

FAKEBASSMIDI_API BOOL BASS_MIDI_StreamEvents(
    HSTREAM handle,
    DWORD mode,
    void *events,
    DWORD length)
{
    // ストリームの有効性チェック
    MIDIStreamState *streamState = find_midi_stream(handle);
    if (streamState == NULL || !streamState->active || events == NULL || length == 0)
    {
        printf("BASS_MIDI_StreamEvents: Invalid parameters\n");
        return FALSE;
    }

    // NORSTATUSとRAWフラグの両方がセットされているかをチェック
    if ((mode & BASS_MIDI_EVENTS_NORSTATUS) && (mode & BASS_MIDI_EVENTS_RAW))
    {
        // 可変長配列としてイベントを処理
        uint32_t *midiEvents = (uint32_t *)events;

        // すべてのイベントをバッファに追加
        BOOL allSucceeded = TRUE;
        for (DWORD i = 0; i < length; i++)
        {
            uint32_t midiData = midiEvents[i];

            // view midievent as 3 bytes
            uint8_t status = midiData & 0xFF;        // 最初のバイトはステータス
            uint8_t data1 = (midiData >> 8) & 0xFF;  // 2番目のバイトはデータ1
            uint8_t data2 = (midiData >> 16) & 0xFF; // 3番目のバイトはデータ2

            uint32_t ksynthMidiData = status | (data1 << 8) | (data2 << 16);

            // バッファにイベントを追加
            if (!add_to_midi_buffer(ksynthMidiData))
            {
                printf("BASS_MIDI_StreamEvents: Failed to add MIDI event to buffer at index %lu (buffer full?)\n", i);
                allSucceeded = FALSE;
                break;
            }
        }

        // イベントがすべて正常にバッファに追加されたら、ミューテックスでKSynthへのアクセスを保護して処理
        if (allSucceeded && streamState->synth != NULL)
        {
            mutex_lock(&streamState->synthMutex);
            process_midi_buffer(streamState->synth);
            mutex_unlock(&streamState->synthMutex);
        }

        return allSucceeded;
    }

    return TRUE;
}

static int g_initialized = 0;

static void BASSMIDI_InitCallback(const char *eventName, void *eventData)
{
    if (eventData == NULL)
    {
        return;
    }

    typedef struct
    {
        DWORD sampleRate;
        DWORD flags;
        int numChannels;
    } InitEventData;

    InitEventData *data = (InitEventData *)eventData;
    g_config.sampleRate = data->sampleRate;
    g_config.numChannels = data->numChannels;
    g_config.initialized = TRUE;

    printf("BASSMIDI: Initialized with sample rate %lu Hz, %d channels\n",
           g_config.sampleRate, g_config.numChannels);
}

static void BASSMIDI_SetMaxVoicesCallback(const char *eventName, void *eventData)
{
    if (eventData == NULL)
    {
        return;
    }

    float *valuePtr = (float *)eventData;
    printf("BASSMIDI: Setting maximum voices to %d\n", (int)(*valuePtr));
    ksynth_set_max_polyphony(
        g_midiStreams[0].synth,
        (uint32_t)(*valuePtr));
}

static void BASSMIDI_AudioDataCallback(const char *eventName, void *eventData);
static void BASSMIDI_SetAttributeCallback(const char *eventName, void *eventData);

static void ensure_bassmidi_initialized(void)
{
    if (g_initialized)
        return;

    printf("BASSMIDI: Initializing library\n");

    // MIDIイベントバッファとキューのメモリを確保
    g_midiEventBuffer = (uint32_t *)malloc(MAX_MIDI_BUFFER_SIZE * sizeof(uint32_t));
    if (g_midiEventBuffer == NULL)
    {
        printf("BASSMIDI: Failed to allocate memory for MIDI event buffer\n");
        return;
    }

    Event_Listen("BASS:INIT", BASSMIDI_InitCallback);
    Event_Listen("BASSMIDI:AUDIO_DATA", BASSMIDI_AudioDataCallback);
    Event_Listen("BASSMIDI:SET_ATTRIBUTE", BASSMIDI_SetAttributeCallback);
    Event_Listen("BASSMIDI:SET_MAX_VOICES", BASSMIDI_SetMaxVoicesCallback);
    Event_Listen("BASS_FreeStream", handle_free_stream_event);
    g_initialized = 1;
}

// Forward declaration only, declaration is at the top of the file

static HSTREAM add_midi_stream(DWORD sampleRate, int numChannels)
{
    for (int i = 0; i < MAX_MIDI_STREAMS; i++)
    {
        if (!g_midiStreams[i].active)
        {
            g_midiStreams[i].handle = i + 1;
            g_midiStreams[i].active = TRUE;
            // サンプルレートの安全な制限（8kHz〜192kHz）
            if (sampleRate < 8000.0f)
                sampleRate = 44100.0f;
            if (sampleRate > 192000.0f)
                sampleRate = 44100.0f;
            g_midiStreams[i].sampleRate = sampleRate;

            g_midiStreams[i].numChannels = numChannels;

            // ミューテックスの初期化
            mutex_init(&g_midiStreams[i].synthMutex);

            // make sample for synth
            struct KSynthSampleMapPtr *sampleMap = ksynth_sample_map_new();
            if (sampleMap == NULL)
            {
                printf("BASSMIDI: Failed to create sample map\n");
                return 0; // エラー時は0を返す
            }            // Generate piano samples using predefined_sample
            for (int key = 0; key < 128; key++)
            {
                // Calculate frequency based on MIDI key number (A4 = key 69 = 440Hz)
                float freq = 440.0f * powf(2.0f, (key - 69) / 12.0f);
                
                // Generate a piano sample for the given key
                size_t numSamples = (size_t)(sampleRate * 5.0f); // 5秒のサンプル
                int16_t *sampleData = (int16_t *)malloc(numSamples * sizeof(int16_t));
                if (sampleData == NULL)
                {
                    printf("BASSMIDI: Failed to allocate memory for sample data\n");
                    ksynth_sample_map_free(sampleMap);
                    return 0; // エラー時は0を返す
                }                // デバッグ情報出力
                printf("BASSMIDI Debug: Creating piano sample for key=%d, freq=%.2fHz, sampleRate=%d, numSamples=%zu\n",
                       key, freq, sampleRate, numSamples);

                // predefined_sample.c のピアノサンプル生成関数を使用
                if (!generate_piano_sample(sampleRate, freq, numSamples, sampleData))
                {
                    printf("BASSMIDI: Failed to generate piano sample for key %d\n", key);
                    free(sampleData);
                    ksynth_sample_map_free(sampleMap);
                    return 0;
                }

                // デバッグ出力（必要に応じて有効化）
                // printf("BASSMIDI Debug: Piano sample generated successfully for key %d\n", key);

                // Add the sample to the map
                ksynth_sample_map_add_sample(
                    sampleMap,
                    (uint8_t)key,
                    (uint32_t)sampleRate,
                    sampleData,
                    (uint8_t)numChannels,
                    numSamples,
                    NULL);

                // Free the sample data after adding to the map
                free(sampleData);
            }

            // Initialize the synthesizer instance
            g_midiStreams[i].synth = ksynth_new(
                (uint32_t)sampleRate,
                numChannels,
                5000,
                ksynth_ms_to_sample((uint32_t)sampleRate, 100),
                sampleMap);
            return g_midiStreams[i].handle;
        }
    }

    // No available streams, return error
    return 0;
}

static MIDIStreamState *find_midi_stream(HSTREAM handle)
{
    if (handle <= 0 || handle > MAX_MIDI_STREAMS)
        return NULL;

    for (int i = 0; i < MAX_MIDI_STREAMS; i++)
    {
        if (g_midiStreams[i].active && g_midiStreams[i].handle == handle)
        {
            return &g_midiStreams[i];
        }
    }

    printf("BASSMIDI: No active stream found for handle %lu\n", handle);
    return NULL;
}

FAKEBASSMIDI_API HSTREAM BASS_MIDI_StreamCreate(
    DWORD channels, // 元は MIDI チャンネル数(1-128)だが、制限しない
    DWORD flags,
    DWORD freq)
{
    ensure_bassmidi_initialized();

    // MIDIキューの初期化
    if (!MIDI_QueueInit())
    {
        printf("BASSMIDI: Failed to initialize MIDI queue\n");
        return 0;
    }

    // MIDIチャンネルパラメータは無視して、常に2チャンネル（ステレオ）で初期化
    HSTREAM streamId = add_midi_stream((DWORD)freq, 2);
    if (streamId == 0)
    {
        return 0;
    }

    return streamId;
}

static void BASSMIDI_AudioDataCallback(const char *eventName, void *eventData)
{
    if (eventData == NULL)
    {
        return;
    }

    typedef struct
    {
        HSTREAM handle;
        void *buffer;
        DWORD length;
        DWORD actualLength;
        int processed;
    } GetDataEventData;

    GetDataEventData *data = (GetDataEventData *)eventData;

    MIDIStreamState *streamState = find_midi_stream(data->handle);

    if (streamState == NULL)
    {
        data->processed = 0;
        if (data->buffer && data->actualLength > 0)
        {
            memset(data->buffer, 0, data->actualLength);
        }
        return;
    } // キュー内のMIDIイベントを処理
    // この時点でオーディオスレッドで実行されているため安全にキューにアクセス可能
    process_all_midi_events(streamState->synth);

    // オーディオバッファの処理
    if (data->buffer != NULL && data->actualLength > 0 && data->actualLength % sizeof(float) == 0)
    {
        // シンセサイザーにオーディオバッファを生成させる
        // KSynthのAPI呼び出し: ksynth_fill_buffer
        BOOL ret = ksynth_fill_buffer(
            streamState->synth,
            (float *)data->buffer,
            data->actualLength / sizeof(float));

        // 処理できた場合は成功フラグをセット
        data->processed = ret ? 1 : 0;
    }
    else
    {
        data->processed = 0;
    }
}

// 属性設定イベントを処理するコールバック
static void BASSMIDI_SetAttributeCallback(const char *eventName, void *eventData)
{
    if (eventData == NULL)
    {
        return;
    }

    // 属性設定イベントのデータ構造
    typedef struct
    {
        HSTREAM handle;
        DWORD attrib;
        float value;
    } SetAttributeEventData;

    SetAttributeEventData *data = (SetAttributeEventData *)eventData;

    // 属性タイプに応じた処理
    switch (data->attrib)
    {
    // 必要に応じて属性を処理
    default:
        printf("BASSMIDI: Unhandled attribute %lu with value %.2f for stream %lu\n",
               data->attrib, data->value, data->handle);
        break;
    }
}

// MIDIストリームを解放する内部関数
static BOOL free_midi_stream(HSTREAM handle)
{
    // MIDI ストリームを見つける
    MIDIStreamState *stream = find_midi_stream(handle);
    if (stream == NULL)
    {
        printf("BASSMIDI: ERROR - Invalid stream handle %lu\n", handle);
        return FALSE;
    }

    // ストリームを非アクティブ化
    stream->active = FALSE;

    // KSynthインスタンスを解放する前にミューテックスで保護
    mutex_lock(&stream->synthMutex);
    if (stream->synth)
    {
        ksynth_free(stream->synth);
        stream->synth = NULL;
    }
    mutex_unlock(&stream->synthMutex);

    // ミューテックスを破棄
    mutex_destroy(&stream->synthMutex);

    printf("BASSMIDI: Stream %lu freed successfully\n", handle);

    return TRUE;
}

// BASS_FreeStreamイベントのハンドラ
static void handle_free_stream_event(const char *eventName, void *eventData)
{
    // イベントデータから必要な情報を取り出す
    typedef struct
    {
        HSTREAM handle;
    } FreeStreamEventData;

    FreeStreamEventData *data = (FreeStreamEventData *)eventData;

    // 内部関数を使ってストリームを解放
    BOOL result = free_midi_stream(data->handle);

    if (result)
    {
        printf("BASSMIDI: Successfully freed stream %lu via event\n", data->handle);
    }
    else
    {
        printf("BASSMIDI: Failed to free stream %lu via event\n", data->handle);
    }
}

/**
 * キュー内のすべてのMIDIイベントを処理する
 * @param synth KSynthインスタンスへのポインタ
 */
static void process_all_midi_events(struct KSynthPtr *synth)
{
    if (synth == NULL)
    {
        return;
    }

    // キュー内のすべてのイベントを処理
    process_midi_buffer(synth);
}

// MIDIイベントをキューに追加（プロデューサー用関数）
static BOOL enqueue_midi_event(uint32_t midiData)
{

    return true;
}

// MIDIイベントをキューから取得 (コンシューマー/オーディオスレッド用関数)
static BOOL dequeue_midi_event(uint32_t *outMidiData)
{
    return TRUE;
}

// MIDIイベントをバッファに追加
static BOOL add_to_midi_buffer(uint32_t midiData)
{
    return MIDI_QueuePush(midiData);
}

/**
 * KSynthにMIDIコマンドを送信する
 * @param synth KSynthインスタンスへのポインタ
 * @param midiData MIDIコマンドデータ
 */
void KSynthSendMIDI(struct KSynthPtr *synth, uint32_t midiData)
{
    if (synth != NULL)
    {
        ksynth_queue_midi_cmd(synth, midiData);
    }
}

// MIDIバッファ内のすべてのイベントを処理する関数
static void process_midi_buffer(struct KSynthPtr *synth)
{
    if (synth == NULL)
    {
        return;
    }

    uint32_t midiData;

    // キュー内のすべてのイベントを処理
    while (MIDI_QueuePop(&midiData))
    {
        // MIDIイベントをKSynthに送信
        KSynthSendMIDI(synth, midiData);
    }
}

void BASS_MIDI_Free()
{
    // MIDIキューの解放
    MIDI_QueueFree();
}
