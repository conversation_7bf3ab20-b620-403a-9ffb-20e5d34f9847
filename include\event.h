#ifndef EVENT_H
#define EVENT_H

#include <stdbool.h>

#ifdef _WIN32
#define EVENT_API __declspec(dllexport)
#else
#define EVENT_API
#endif

#ifdef __cplusplus
extern "C" {
#endif

/**
 * イベントコールバック関数の型定義
 * @param eventName イベント名
 * @param data イベントデータ
 */
typedef void (*EventCallback)(const char* eventName, void* data);

/**
 * イベントシステムの初期化
 */
EVENT_API void Event_Init(void);

/**
 * イベントシステムの終了処理
 */
EVENT_API void Event_Shutdown(void);

/**
 * イベントリスナーの登録
 * @param eventName 監視するイベント名
 * @param callback コールバック関数
 * @return リスナーID（0より大きい値）、エラー時は0
 */
EVENT_API int Event_Listen(const char* eventName, EventCallback callback);

/**
 * イベントリスナーの削除
 * @param listenerId リスナーID
 * @return 成功した場合はtrue
 */
EVENT_API bool Event_Remove(int listenerId);

/**
 * イベント発火
 * @param eventName イベント名
 * @param data イベントデータ
 */
EVENT_API void Event_Trigger(const char* eventName, void* data);

#ifdef __cplusplus
}
#endif

#endif // EVENT_H
