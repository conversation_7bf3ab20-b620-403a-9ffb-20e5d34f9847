#include "../include/predefined_sample.h"
#include <math.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

typedef struct {
    float multiplier;
    float amplitude;
} Harmonic;

// Rustのrandom_rangeに相当する関数
static float random_range(float min, float max) {
    return min + (max - min) * ((float)rand() / (float)RAND_MAX);
}

int generate_piano_sample(uint32_t sample_rate, float freq, size_t sample_count, int16_t* samples) {
    if (!samples) return 0;

    // 乱数初期化（Rustのrand::rngに相当）
    static int seeded = 0;
    if (!seeded) {
        srand((unsigned int)time(NULL));
        seeded = 1;
    }    // Rustコードのvec!とpush()に相当するハーモニクスの初期化
    Harmonic harmonics[15]; // Rustの可変長配列と違い、最大サイズを静的に確保する必要がある
    int harmonic_count = 0;

    // 基本ハーモニクス - Rustコードの初期ベクターと同等
    harmonics[harmonic_count].multiplier = 1.0f;
    harmonics[harmonic_count].amplitude = 1.0f;
    harmonic_count++;
    
    harmonics[harmonic_count].multiplier = 2.01f;
    harmonics[harmonic_count].amplitude = 0.5f;
    harmonic_count++;
    
    harmonics[harmonic_count].multiplier = 3.02f;
    harmonics[harmonic_count].amplitude = 0.3f;
    harmonic_count++;
    
    harmonics[harmonic_count].multiplier = 4.98f;
    harmonics[harmonic_count].amplitude = 0.2f;
    harmonic_count++;
    
    harmonics[harmonic_count].multiplier = 6.1f;
    harmonics[harmonic_count].amplitude = 0.1f;
    harmonic_count++;

    // 条件付き追加 - Rustコードのpush()と同等
    if (freq < 400.0f) {
        harmonics[harmonic_count].multiplier = 0.5f;
        harmonics[harmonic_count].amplitude = 0.4f;
        harmonic_count++;
        
        harmonics[harmonic_count].multiplier = 7.03f;
        harmonics[harmonic_count].amplitude = 0.12f;
        harmonic_count++;
        
        harmonics[harmonic_count].multiplier = 8.97f;
        harmonics[harmonic_count].amplitude = 0.08f;
        harmonic_count++;
        
        harmonics[harmonic_count].multiplier = 10.09f;
        harmonics[harmonic_count].amplitude = 0.06f;
        harmonic_count++;

        if (freq < 150.0f) {
            harmonics[harmonic_count].multiplier = 0.25f;
            harmonics[harmonic_count].amplitude = 0.15f;
            harmonic_count++;
            
            harmonics[harmonic_count].multiplier = 1.5f;
            harmonics[harmonic_count].amplitude = 0.3f;
            harmonic_count++;
        }
    }    // Rustコードの Vec<f32> に相当する位相シフト配列
    float phase_shifts[15];
    for (int i = 0; i < harmonic_count; ++i) {
        phase_shifts[i] = random_range(0.0f, 2.0f * M_PI);
    }

    // Rustと同じ定数値を使用
    const float reference_low = 300.0f; 
    const float reference_high = 20000.0f;

    // 周波数スケーリング - Rustコードと同じ条件分岐と計算式
    float frequency_scaling;
    if (freq < reference_low) {
        if (freq < 80.0f) {
            frequency_scaling = 2.5f + 1.5f * (1.0f - freq / 80.0f);
        } else if (freq < 150.0f) {
            frequency_scaling = 1.8f + 0.7f * (1.0f - freq / 150.0f);
        } else {
            frequency_scaling = 1.3f + 0.5f * (1.0f - freq / reference_low);
        }
    } else if (freq > reference_high) {
        frequency_scaling = expf(-(freq - reference_high) * 0.0005f) * 0.6f;
    } else {
        frequency_scaling = 1.0f;
    }

    // サチュレーション量 - Rustコードと同じ条件分岐と計算式
    float saturation_amount;
    if (freq < 200.0f) {
        saturation_amount = 0.6f + 0.4f * (1.0f - freq / 200.0f);
    } else if (freq < 500.0f) {
        saturation_amount = 0.3f * (1.0f - (freq - 200.0f) / 300.0f);
    } else {
        saturation_amount = 0.0f;
    }    for (size_t i = 0; i < sample_count; ++i) {
        float t = (float)i / (float)sample_rate;
        
        // アタック時間の計算 - Rustコードと同じ条件分岐と計算
        float attack_time;
        if (freq < 150.0f) {
            attack_time = 0.01f + 0.02f * (1.0f - freq / 150.0f);
        } else {
            float temp = reference_high / freq;
            attack_time = 0.005f + 0.01f * (temp < 1.0f ? temp : 1.0f);
        }

        // アタック部分の計算
        float attack;
        if (t < attack_time) {
            attack = t / attack_time;
        } else {
            attack = 1.0f;
        }

        // ディケイ係数の計算
        float decay_factor;
        if (freq < reference_low) {
            float base_decay = 0.5f + 0.5f * powf(freq / reference_low, 0.8f);
            decay_factor = base_decay;
        } else {
            float temp = freq / 440.0f;
            decay_factor = 2.0f + (temp < 1.0f ? temp : 1.0f);
        }        float decay = expf(-decay_factor * t);
        float envelope = attack * decay;

        float sample = 0.0f;
        
        // 各ハーモニクスの合成 - Rustコードのzip処理と同様の処理
        for (int idx = 0; idx < harmonic_count; ++idx) {
            float multiplier = harmonics[idx].multiplier;
            float amplitude = harmonics[idx].amplitude;
            float phase = phase_shifts[idx];
            
            float harmonic_freq = freq * multiplier;

            // 高周波域の減衰
            float harmonic_attenuation;
            if (harmonic_freq > reference_high) {
                harmonic_attenuation = expf(-0.002f * (harmonic_freq - reference_high));
            } else {
                harmonic_attenuation = 1.0f;
            }
            
            // 倍音ごとの減衰
            float harmonic_decay = expf(-t * (1.0f + idx * 0.4f));

            // 低周波域の音色調整
            float harmonic_boost;
            if (freq < 200.0f) {
                if (multiplier < 1.0f) {
                    harmonic_boost = 2.0f;
                } else if (idx <= 3) {
                    harmonic_boost = 1.6f;
                } else {
                    harmonic_boost = 1.0f;
                }
            } else if (freq < 300.0f && (idx <= 2 || multiplier < 1.0f)) {
                harmonic_boost = 1.3f;
            } else {
                harmonic_boost = 1.0f;
            }

            // 正弦波の合成
            sample += sinf(2.0f * M_PI * harmonic_freq * t + phase)
                   * amplitude
                   * harmonic_attenuation
                   * harmonic_decay
                   * harmonic_boost
                   / 2.0f;
        }        // ハンマーノイズの追加 - Rustコードと同じ処理
        float noise_duration = 0.04f;
        float noise_envelope = expf(-60.0f * t);
        float noise = 0.0f;
        
        if (t < noise_duration) {
            float raw = random_range(-1.0f, 1.0f);
            float smooth = (raw + random_range(-1.0f, 1.0f)) * 0.5f;
            noise = smooth * 0.2f * noise_envelope;
        }

        sample = (sample + noise) * envelope * frequency_scaling;

        // サチュレーション効果の適用 - Rustコードと同じ処理
        if (saturation_amount > 0.0f) {
            float gain = 1.0f + saturation_amount * 2.0f;
            sample = tanhf(sample * gain) / gain;

            float distortion_strength = saturation_amount * 0.1f;
            float distorted = sample + (sample * sample * sample) * distortion_strength;
            sample = sample * (1.0f - saturation_amount * 0.3f) + distorted * saturation_amount * 0.3f;
        }

        // 出力ゲインの調整
        float output_gain = (freq < 200.0f) ? 0.6f : 0.4f;
        
        // サンプル値のクリッピング - RustのclampとI16::MAXに相当
        float scaled = sample * 32767.0f * output_gain;
        if (scaled > 32767.0f) {
            scaled = 32767.0f;
        } else if (scaled < -32767.0f) {
            scaled = -32767.0f;
        }

        samples[i] = (int16_t)scaled;
    }

    return 1;
}
