#ifndef MUTEX_H
#define MUTEX_H

#ifdef _WIN32
#include <windows.h>

typedef CRITICAL_SECTION Mutex;

static inline void mutex_init(Mutex *mutex)
{
    InitializeCriticalSection(mutex);
}

static inline void mutex_lock(Mutex *mutex)
{
    EnterCriticalSection(mutex);
}

static inline void mutex_unlock(Mutex *mutex)
{
    LeaveCriticalSection(mutex);
}

static inline void mutex_destroy(Mutex *mutex)
{
    DeleteCriticalSection(mutex);
}

#else
#include <pthread.h>

typedef pthread_mutex_t Mutex;

static inline void mutex_init(Mutex *mutex)
{
    pthread_mutex_init(mutex, NULL);
}

static inline void mutex_lock(Mutex *mutex)
{
    pthread_mutex_lock(mutex);
}

static inline void mutex_unlock(Mutex *mutex)
{
    pthread_mutex_unlock(mutex);
}

static inline void mutex_destroy(Mutex *mutex)
{
    pthread_mutex_destroy(mutex);
}

#endif // _WIN32

#endif // MUTEX_H
