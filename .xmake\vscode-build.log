checking for platform ... windows
checking for architecture ... x64
checking for Microsoft Visual Studio (x64) version ... 2022
checking for Microsoft C/C++ Compiler (x64) version ... 19.40.33811
[ 60%]: compiling.release src\bass.c
[ 60%]: compiling.release src\event.c
[ 60%]: compiling.release src\predefined_sample.c
[ 60%]: compiling.release src\bassmidi.c
error: bass.c
src\bass.c(26): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax
src\bass.c(27): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax
src\bass.c(28): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax
src\bass.c(29): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax
src\bass.c(30): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax
src\bass.c(31): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax
src\bass.c(32): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax
src\bass.c(33): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax
src\bass.c(34): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax
src\bass.c(35): error C4576: a parenthesized type followed by an initializer list is a non-standard explicit type conversion syntax

  > in src\bass.c
