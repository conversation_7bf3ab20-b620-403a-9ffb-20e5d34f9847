#include "bass.h"
#include "bassmidi.h" // Include bassmidi.h to access its functions
#include "event.h"    // Include event system
#include <string.h>   // For memset
#include <stdlib.h>   // For NULL
#include <stdio.h>    // For printf

// Configuration storage
#define MAX_CONFIG_OPTIONS 64
typedef struct {
    DWORD option;
    DWORD value;
    BOOL set;
} ConfigEntry;

static ConfigEntry g_configTable[MAX_CONFIG_OPTIONS];
static BOOL g_configInitialized = FALSE;

// Initialize configuration table with default values
static void init_config_table(void) {
    if (g_configInitialized) return;

    memset(g_configTable, 0, sizeof(g_configTable));

    // Set some default values for common configuration options
    g_configTable[0] = (ConfigEntry){BASS_CONFIG_BUFFER, 500, TRUE};
    g_configTable[1] = (ConfigEntry){BASS_CONFIG_UPDATEPERIOD, 100, TRUE};
    g_configTable[2] = (ConfigEntry){BASS_CONFIG_GVOL_SAMPLE, 10000, TRUE};
    g_configTable[3] = (ConfigEntry){BASS_CONFIG_GVOL_STREAM, 10000, TRUE};
    g_configTable[4] = (ConfigEntry){BASS_CONFIG_GVOL_MUSIC, 10000, TRUE};
    g_configTable[5] = (ConfigEntry){BASS_CONFIG_CURVE_VOL, FALSE, TRUE};
    g_configTable[6] = (ConfigEntry){BASS_CONFIG_CURVE_PAN, FALSE, TRUE};
    g_configTable[7] = (ConfigEntry){BASS_CONFIG_FLOATDSP, FALSE, TRUE};
    g_configTable[8] = (ConfigEntry){BASS_CONFIG_NET_TIMEOUT, 5000, TRUE};
    g_configTable[9] = (ConfigEntry){BASS_CONFIG_NET_BUFFER, 5000, TRUE};

    g_configInitialized = TRUE;
}

FAKEBASS_API BOOL BASS_Init(
    int device,
    DWORD freq,
    DWORD flags,
    HWND win,
    void *clsid)
{ // Initialize the BASS library
    // Initialize the configuration table
    init_config_table();

    // Initialize the event system
    Event_Init();

    // 初期化情報をイベントシステムで配信
    typedef struct
    {
        DWORD sampleRate; // サンプリングレート
        DWORD flags;      // フラグ
        int numChannels;  // チャンネル数（通常は2=ステレオ）
    } InitEventData;

    InitEventData eventData;
    eventData.sampleRate = freq;
    eventData.flags = flags;
    eventData.numChannels = 2;

    Event_Trigger("BASS:INIT", &eventData);

    printf("BASS: Initialized with sample rate %lu Hz\n", freq);

    return TRUE;
}

FAKEBASS_API BOOL BASS_Free(void)
{
    // Free the BASS library resources
    Event_Shutdown();
    printf("BASS: Shutdown completed\n");

    // For the purpose of this fake implementation, we will just return TRUE
    return TRUE;
}

FAKEBASS_API BOOL BASS_SetConfig(
    DWORD option,
    DWORD value)
{
    // Initialize configuration table if not already done
    init_config_table();

    // Find existing entry or use empty slot
    int targetIndex = -1;
    for (int i = 0; i < MAX_CONFIG_OPTIONS; i++) {
        if (g_configTable[i].set && g_configTable[i].option == option) {
            targetIndex = i;
            break;
        }
        if (!g_configTable[i].set && targetIndex == -1) {
            targetIndex = i;
        }
    }

    // Store the configuration value
    if (targetIndex >= 0) {
        g_configTable[targetIndex].option = option;
        g_configTable[targetIndex].value = value;
        g_configTable[targetIndex].set = TRUE;
    }

    // Configuration event data structure
    typedef struct
    {
        DWORD option;
        DWORD value;
    } ConfigEventData;

    ConfigEventData eventData;
    eventData.option = option;
    eventData.value = value;

    // Trigger configuration event
    Event_Trigger("BASS:CONFIG", &eventData);

    // Log the configuration change
    printf("BASS: Configuration option %lu set to %lu\n", option, value);

    return TRUE;
}

FAKEBASS_API DWORD BASS_GetConfig(
    DWORD option)
{
    // Initialize configuration table if not already done
    init_config_table();

    // Search for the configuration option
    for (int i = 0; i < MAX_CONFIG_OPTIONS; i++) {
        if (g_configTable[i].set && g_configTable[i].option == option) {
            printf("BASS: Retrieved configuration option %lu = %lu\n", option, g_configTable[i].value);
            return g_configTable[i].value;
        }
    }

    // If option not found, return 0 (which typically indicates error or default)
    printf("BASS: Configuration option %lu not found, returning 0\n", option);
    return 0;
}

FAKEBASS_API BOOL BASS_ChannelSetAttribute(
    DWORD handle,
    DWORD attrib,
    float value)
{    if (attrib == BASS_ATTRIB_MIDI_VOICES)
    {
        Event_Trigger("BASSMIDI:SET_MAX_VOICES", &value);
    }

    // For a fake implementation, we will just return TRUE
    return TRUE;
}

FAKEBASS_API DWORD BASS_ChannelFlags(
    DWORD handle,
    DWORD flags,
    DWORD mask)
{
    // Set or clear flags for a channel
    // For a fake implementation, we will just return the flags
    if (mask)
    {
        // If a mask is provided, clear the specified flags
        flags &= ~mask;
    }
    // Set the flags
    // For a fake implementation, we will just return the flags
    return flags;
}

FAKEBASS_API DWORD BASS_ChannelGetData(
    HSTREAM handle,
    void *buffer,
    DWORD length)
{
    // This function retrieves audio sample data from a sample channel, or FFT data from any channel
    static int callCount = 0;
    callCount++;

    if (buffer && length > 0)
    {
        // Check for float data format
        if (length & BASS_DATA_FLOAT)
        {
            // Handle as floating point data
            DWORD actualLength = length & 0xfffff; // Remove the flags to get actual byte length

            // イベントデータ構造体
            typedef struct
            {
                HSTREAM handle;
                void *buffer;
                DWORD length;
                DWORD actualLength;
                int processed;
            } GetDataEventData;

            // イベントデータを作成
            GetDataEventData eventData;
            eventData.handle = handle;
            eventData.buffer = buffer;
            eventData.length = length;
            eventData.actualLength = actualLength;
            eventData.processed = 0;

            memset(buffer, 0, actualLength);
            Event_Trigger("BASSMIDI:AUDIO_DATA", &eventData);

            return actualLength;
        }

        return 0; // 0バイト返す
    }

    // If using BASS_DATA_AVAILABLE flag or if buffer is NULL
    if (length == BASS_DATA_AVAILABLE || buffer == NULL)
    {
        // Return a fake amount of data available
        DWORD availableBytes = 1024 * 8; // より多くのデータが利用可能と報告
        return availableBytes;
    }

    return 0; // Return 0 on error
}

// ストリームを解放する関数
FAKEBASS_API BOOL BASS_StreamFree(HSTREAM handle)
{
    // イベントを発行してストリームの解放を要求
    typedef struct
    {
        HSTREAM handle;
    } FreeStreamEventData;

    FreeStreamEventData eventData;
    eventData.handle = handle;

    Event_Trigger("BASS_FreeStream", &eventData);

    BOOL result = TRUE;

    if (result)
    {
        printf("BASS: Stream %lu freed successfully\n", handle);
    }
    else
    {
        printf("BASS: ERROR - Failed to free stream %lu\n", handle);
    }

    return result;
}
