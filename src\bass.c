#include "bass.h"
#include "bassmidi.h" // Include bassmidi.h to access its functions
#include "event.h"    // Include event system
#include <string.h>   // For memset
#include <stdlib.h>   // For NULL
#include <stdio.h>    // For printf

FAKEBASS_API BOOL BASS_Init(
    int device,
    DWORD freq,
    DWORD flags,
    HWND win,
    void *clsid)
{ // Initialize the BASS library
    // For the purpose of this fake implementation, we will just return TRUE    // Initialize the event system
    Event_Init();

    // 初期化情報をイベントシステムで配信
    typedef struct
    {
        DWORD sampleRate; // サンプリングレート
        DWORD flags;      // フラグ
        int numChannels;  // チャンネル数（通常は2=ステレオ）
    } InitEventData;

    InitEventData eventData;
    eventData.sampleRate = freq;
    eventData.flags = flags;
    eventData.numChannels = 2;

    Event_Trigger("BASS:INIT", &eventData);

    printf("BASS: Initialized with sample rate %lu Hz\n", freq);

    return TRUE;
}

FAKEBASS_API BOOL BASS_Free(void)
{
    // Free the BASS library resources
    Event_Shutdown();
    printf("BASS: Shutdown completed\n");

    // For the purpose of this fake implementation, we will just return TRUE
    return TRUE;
}

FAKEBASS_API BOOL BASS_SetConfig(
    DWORD option,
    DWORD value)
{
    // Configuration event data structure
    typedef struct
    {
        DWORD option;
        DWORD value;
    } ConfigEventData;

    ConfigEventData eventData;
    eventData.option = option;
    eventData.value = value;

    // Trigger configuration event
    Event_Trigger("BASS:CONFIG", &eventData);

    // Log the configuration change
    printf("BASS: Configuration option %lu set to %lu\n", option, value);

    // For a fake implementation, we will just return TRUE
    return TRUE;
}

FAKEBASS_API BOOL BASS_ChannelSetAttribute(
    DWORD handle,
    DWORD attrib,
    float value)
{    if (attrib == BASS_ATTRIB_MIDI_VOICES)
    {
        Event_Trigger("BASSMIDI:SET_MAX_VOICES", &value);
    }

    // For a fake implementation, we will just return TRUE
    return TRUE;
}

FAKEBASS_API DWORD BASS_ChannelFlags(
    DWORD handle,
    DWORD flags,
    DWORD mask)
{
    // Set or clear flags for a channel
    // For a fake implementation, we will just return the flags
    if (mask)
    {
        // If a mask is provided, clear the specified flags
        flags &= ~mask;
    }
    // Set the flags
    // For a fake implementation, we will just return the flags
    return flags;
}

FAKEBASS_API DWORD BASS_ChannelGetData(
    HSTREAM handle,
    void *buffer,
    DWORD length)
{
    // This function retrieves audio sample data from a sample channel, or FFT data from any channel
    static int callCount = 0;
    callCount++;

    if (buffer && length > 0)
    {
        // Check for float data format
        if (length & BASS_DATA_FLOAT)
        {
            // Handle as floating point data
            DWORD actualLength = length & 0xfffff; // Remove the flags to get actual byte length

            // イベントデータ構造体
            typedef struct
            {
                HSTREAM handle;
                void *buffer;
                DWORD length;
                DWORD actualLength;
                int processed;
            } GetDataEventData;

            // イベントデータを作成
            GetDataEventData eventData;
            eventData.handle = handle;
            eventData.buffer = buffer;
            eventData.length = length;
            eventData.actualLength = actualLength;
            eventData.processed = 0;

            memset(buffer, 0, actualLength);
            Event_Trigger("BASSMIDI:AUDIO_DATA", &eventData);

            return actualLength;
        }

        return 0; // 0バイト返す
    }

    // If using BASS_DATA_AVAILABLE flag or if buffer is NULL
    if (length == BASS_DATA_AVAILABLE || buffer == NULL)
    {
        // Return a fake amount of data available
        DWORD availableBytes = 1024 * 8; // より多くのデータが利用可能と報告
        return availableBytes;
    }

    return 0; // Return 0 on error
}

// ストリームを解放する関数
FAKEBASS_API BOOL BASS_StreamFree(HSTREAM handle)
{
    // イベントを発行してストリームの解放を要求
    typedef struct
    {
        HSTREAM handle;
    } FreeStreamEventData;

    FreeStreamEventData eventData;
    eventData.handle = handle;

    Event_Trigger("BASS_FreeStream", &eventData);

    BOOL result = TRUE;

    if (result)
    {
        printf("BASS: Stream %lu freed successfully\n", handle);
    }
    else
    {
        printf("BASS: ERROR - Failed to free stream %lu\n", handle);
    }

    return result;
}
